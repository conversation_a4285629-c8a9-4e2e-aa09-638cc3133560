import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import { createPinia } from 'pinia'
import router from './router'
import App from './App.vue'
import { onCLS, onFID, onLCP } from 'web-vitals'
import './views/global-theme.css'

const app = createApp(App)
app.use(ElementPlus, {
  locale: zhCn,
})
app.use(createPinia())
app.use(router)

onCLS(console.log)
onFID(console.log)
onLCP(console.log)

window.onerror = function (msg, url, line, col, error) {
  // 可扩展为上报到后端
  console.error('前端错误捕获:', msg, url, line, col, error)
}

// 预留CSRF Token处理结构
// axios.defaults.headers.common['X-CSRF-Token'] = localStorage.getItem('csrfToken') || ''

app.mount('#app') 