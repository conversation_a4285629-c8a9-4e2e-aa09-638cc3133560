<template>
  <div class="erp-dashboard-page">
    <div class="erp-stats-row">
      <div class="erp-stat-card">
        <div class="erp-stat-label">本月采购金额</div>
        <div class="erp-stat-value">¥ 52,000</div>
        <i class="fas fa-shopping-cart erp-stat-icon blue"></i>
      </div>
      <div class="erp-stat-card">
        <div class="erp-stat-label">待审批单据</div>
        <div class="erp-stat-value">3</div>
        <i class="fas fa-file-signature erp-stat-icon green"></i>
      </div>
      <div class="erp-stat-card">
        <div class="erp-stat-label">供应商数量</div>
        <div class="erp-stat-value">8</div>
        <i class="fas fa-industry erp-stat-icon purple"></i>
      </div>
      <div class="erp-stat-card">
        <div class="erp-stat-label">操作</div>
        <button class="btn-blue" @click="addPurchase">
          <i class="fas fa-plus-circle"></i>
          <span>新建采购申请</span>
        </button>
      </div>
    </div>
    <div class="erp-card">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="采购管理" name="purchase">
          <ErpTable :data="purchaseList">
            <el-table-column prop="no" label="单号" />
            <el-table-column prop="applicant" label="申请人" />
            <el-table-column prop="type" label="类型" />
            <el-table-column prop="amount" label="金额">
              <template #default="scope">
                ¥{{ scope.row.amount.toLocaleString('zh-CN') }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" />
            <el-table-column prop="date" label="日期" />
            <el-table-column label="操作">
              <template #default="scope">
                <div class="action-buttons">
                  <button class="btn-blue-light compact" @click="editPurchase(scope.row)">
                    <i class="fas fa-edit"></i>
                    <span>编辑</span>
                  </button>
                </div>
              </template>
            </el-table-column>
          </ErpTable>
        </el-tab-pane>
        <el-tab-pane label="供应商管理" name="supplier">
          <ErpTable :data="supplierList">
            <el-table-column prop="name" label="供应商名称" />
            <el-table-column prop="contact" label="联系人" />
            <el-table-column prop="phone" label="联系电话" />
            <el-table-column prop="level" label="等级" />
            <el-table-column prop="lastOrder" label="最近订单" />
            <el-table-column prop="amount" label="合作金额">
              <template #default="scope">
                ¥{{ scope.row.amount.toLocaleString('zh-CN') }}
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template #default="scope">
                <div class="action-buttons">
                  <button class="btn-blue-light compact" @click="editSupplier(scope.row)">
                    <i class="fas fa-edit"></i>
                    <span>编辑</span>
                  </button>
                </div>
              </template>
            </el-table-column>
          </ErpTable>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>

  <!-- 新建/编辑采购申请弹窗 -->
  <el-dialog v-model="dialogVisible" :title="dialogMode === 'add' ? '新建采购申请' : '编辑采购申请'" width="600px">
    <el-form :model="form" label-width="100px">
      <el-form-item label="申请单号">
        <el-input v-model="form.no" placeholder="自动生成" :disabled="dialogMode === 'add'" />
      </el-form-item>
      <el-form-item label="申请人">
        <el-input v-model="form.applicant" placeholder="请输入申请人姓名" />
      </el-form-item>
      <el-form-item label="采购类型">
        <el-select v-model="form.type" placeholder="请选择采购类型" style="width: 100%">
          <el-option label="原材料采购" value="原材料采购" />
          <el-option label="设备采购" value="设备采购" />
          <el-option label="办公用品" value="办公用品" />
          <el-option label="维修配件" value="维修配件" />
          <el-option label="其他" value="其他" />
        </el-select>
      </el-form-item>
      <el-form-item label="采购金额">
        <el-input v-model="form.amount" type="number" placeholder="请输入采购金额" />
      </el-form-item>
      <el-form-item label="申请日期">
        <el-date-picker
          v-model="form.date"
          type="date"
          placeholder="选择申请日期"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="采购说明">
        <el-input v-model="form.description" type="textarea" placeholder="请输入采购说明和要求" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="flex justify-end space-x-4">
        <button class="btn-gray" @click="dialogVisible = false">
          <i class="fas fa-times"></i>
          <span>取消</span>
        </button>
        <button class="btn-blue" @click="savePurchase">
          <i class="fas fa-save"></i>
          <span>保存</span>
        </button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ErpTable from '../components/common/ErpTable.vue'

// 定义采购申请表单数据类型
interface PurchaseForm {
  id: number
  no: string
  applicant: string
  type: string
  amount: number | string
  date: string
  description: string
}

const activeTab = ref('purchase')

// 弹窗相关状态
const dialogVisible = ref(false)
const dialogMode = ref<'add' | 'edit'>('add')
const form = ref<PurchaseForm>({
  id: 0,
  no: '',
  applicant: '',
  type: '',
  amount: '',
  date: '',
  description: ''
})

// 合并的采购管理数据（申请+审批+订单）
const purchaseList = ref([
  { no: 'PA-001', applicant: '王五', type: '申请', amount: 12000, status: '待审批', date: '2024-06-01' },
  { no: 'PA-002', applicant: '赵六', type: '申请', amount: 8000, status: '已通过', date: '2024-06-02' },
  { no: 'PA-003', applicant: '钱七', type: '申请', amount: 15000, status: '待审批', date: '2024-06-03' },
  { no: 'PO-001', applicant: '李四', type: '订单', amount: 5000, status: '已下单', date: '2024-06-01' },
  { no: 'PO-002', applicant: '王五', type: '订单', amount: 4800, status: '待发货', date: '2024-06-02' },
  { no: 'PO-003', applicant: '张三', type: '订单', amount: 6200, status: '已完成', date: '2024-05-28' },
  { no: 'PA-004', applicant: '刘八', type: '申请', amount: 9500, status: '已驳回', date: '2024-06-04' }
])

// 优化的供应商管理数据
const supplierList = ref([
  { name: '钢铁供应商', contact: '张经理', phone: '13800138000', level: 'A', lastOrder: '2024-06-01', amount: 25000 },
  { name: '铝材供应商', contact: '李经理', phone: '13900139000', level: 'B', lastOrder: '2024-05-28', amount: 18000 },
  { name: '塑料制品厂', contact: '王总', phone: '13700137000', level: 'A', lastOrder: '2024-06-03', amount: 32000 },
  { name: '电子元件商', contact: '赵主管', phone: '13600136000', level: 'B', lastOrder: '2024-05-25', amount: 15000 },
  { name: '包装材料商', contact: '孙经理', phone: '13500135000', level: 'C', lastOrder: '2024-05-20', amount: 8000 },
  { name: '化工原料商', contact: '周总', phone: '13400134000', level: 'A', lastOrder: '2024-06-02', amount: 42000 },
  { name: '机械设备商', contact: '吴经理', phone: '13300133000', level: 'B', lastOrder: '2024-05-30', amount: 28000 }
])

// 新建采购申请
function addPurchase() {
  dialogMode.value = 'add'
  form.value = {
    id: 0,
    no: generatePurchaseNo(),
    applicant: '',
    type: '',
    amount: '',
    date: new Date().toISOString().split('T')[0],
    description: ''
  }
  dialogVisible.value = true
}

// 编辑采购申请
function editPurchase(row: any) {
  dialogMode.value = 'edit'
  form.value = {
    id: row.id || 0,
    no: row.no,
    applicant: row.applicant,
    type: row.type,
    amount: row.amount,
    date: row.date,
    description: row.description || ''
  }
  dialogVisible.value = true
}

// 生成采购申请单号
function generatePurchaseNo(): string {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
  return `PA-${year}${month}${day}-${random}`
}

// 保存采购申请
function savePurchase() {
  if (!form.value.applicant || !form.value.type || !form.value.amount) {
    console.warn('请填写完整信息')
    return
  }

  const amount = typeof form.value.amount === 'string'
    ? parseFloat(form.value.amount)
    : form.value.amount

  if (dialogMode.value === 'add') {
    // 新增采购申请
    const newPurchase = {
      no: form.value.no,
      applicant: form.value.applicant,
      type: '申请',
      amount: amount,
      status: '待审批',
      date: form.value.date
    }
    purchaseList.value.unshift(newPurchase)
  } else {
    // 编辑采购申请
    const index = purchaseList.value.findIndex(p => p.no === form.value.no)
    if (index !== -1) {
      purchaseList.value[index] = {
        ...purchaseList.value[index],
        applicant: form.value.applicant,
        amount: amount,
        date: form.value.date
      }
    }
  }

  dialogVisible.value = false
}

function editSupplier(row: any) { /* TODO: 编辑供应商逻辑 */ }
</script>

<style scoped>
.erp-dashboard-page {
  padding-bottom: 20px;
}
/* Purchase页面特有样式 - 基础样式已在全局CSS中定义 */

/* 保留页面特有的按钮样式 */
.erp-flat-btn {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
}
.erp-flat-btn i {
  margin-right: 4px;
}
.erp-flat-btn span {
  display: inline-block;
  white-space: nowrap;
}
</style> 