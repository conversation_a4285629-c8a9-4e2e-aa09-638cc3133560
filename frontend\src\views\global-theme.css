/* ========================================
   ERP系统全局样式文件 - 重新整理版本
   ======================================== */

/* ========================================
   1. CSS变量定义 - 支持明暗模式切换
   ======================================== */

:root {
  /* === 浅色模式 - 现代化玻璃拟态风格 === */
  
  /* 背景色系 */
  --bg-primary: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  --bg-secondary: rgba(248, 250, 252, 0.8);
  --bg-tertiary: rgba(241, 245, 249, 0.6);
  --bg-card: rgba(255, 255, 255, 0.7);
  --bg-sidebar: linear-gradient(145deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.8) 100%);
  --bg-header: #ffffff;
  
  /* 文字色系 */
  --text-primary: #1a1a2e;
  --text-secondary: #2d2d44;
  --text-tertiary: #4a4a5a;
  
  /* 边框色系 */
  --border-color: rgba(226, 232, 240, 0.3);
  --border-hover: rgba(148, 163, 184, 0.4);
  
  /* 主题色系 */
  --accent-primary: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  --accent-secondary: linear-gradient(135deg, #06b6d4 0%, #3b82f6 100%);
  --accent-success: linear-gradient(135deg, #10b981 0%, #059669 100%);
  --accent-warning: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  --accent-danger: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  --accent-primary-alpha: rgba(99, 102, 241, 0.15);
  
  /* 阴影系统 */
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* 玻璃拟态效果 */
  --glass-bg: rgba(255, 255, 255, 0.4);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  
  /* 发光效果 */
  --glow-primary: 0 0 20px rgba(99, 102, 241, 0.3);
  --glow-secondary: 0 0 20px rgba(6, 182, 212, 0.3);
  --glow-success: 0 0 20px rgba(16, 185, 129, 0.3);

  /* ERP系统专用色彩 */
  --erp-primary: #2563eb;
  --erp-success: #10b981;
  --erp-warning: #f59e42;
  --erp-danger: #ef4444;
  --erp-bg: #f1f5f9;
  --erp-sidebar: #1e293b;
  --erp-sidebar-active: #334155;
  --erp-border-radius: 12px;
  --erp-shadow: 0 2px 8px 0 rgba(0,0,0,0.04);
}

[data-theme="dark"] {
  /* === 深色模式 - 现代化玻璃拟态风格 === */
  
  /* 背景色系 */
  --bg-primary: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  --bg-secondary: rgba(30, 41, 59, 0.8);
  --bg-tertiary: rgba(51, 65, 85, 0.6);
  --bg-card: rgba(30, 41, 59, 0.7);
  --bg-sidebar: linear-gradient(145deg, rgba(30, 41, 59, 0.9) 0%, rgba(51, 65, 85, 0.8) 100%);
  --bg-header: #1e293b;
  
  /* 文字色系 */
  --text-primary: #f8fafc;
  --text-secondary: #e2e8f0;
  --text-tertiary: #cbd5e1;
  
  /* 边框色系 */
  --border-color: rgba(51, 65, 85, 0.3);
  --border-hover: rgba(100, 116, 139, 0.4);
  
  /* 主题色系 */
  --accent-primary: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  --accent-secondary: linear-gradient(135deg, #06b6d4 0%, #3b82f6 100%);
  --accent-success: linear-gradient(135deg, #10b981 0%, #059669 100%);
  --accent-warning: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  --accent-danger: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  --accent-primary-alpha: rgba(99, 102, 241, 0.2);
  
  /* 阴影系统 */
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.5), 0 2px 4px -1px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.6), 0 4px 6px -2px rgba(0, 0, 0, 0.5);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.7), 0 10px 10px -5px rgba(0, 0, 0, 0.6);
  
  /* 玻璃拟态效果 */
  --glass-bg: rgba(30, 41, 59, 0.4);
  --glass-border: rgba(51, 65, 85, 0.2);
  --glass-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.6);
  
  /* 发光效果 */
  --glow-primary: 0 0 25px rgba(99, 102, 241, 0.4);
  --glow-secondary: 0 0 25px rgba(6, 182, 212, 0.4);
  --glow-success: 0 0 25px rgba(16, 185, 129, 0.4);
}

/* ========================================
   2. 全局基础样式
   ======================================== */

/* 全局样式 - 现代化玻璃拟态风格 */
body {
  background: var(--erp-bg);
  color: #1a1a2e;
  font-family: 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
  margin: 0;
  padding: 0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  line-height: 1.6;
  font-weight: 500;
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
}

/* 全局重置 */
* {
  box-sizing: border-box;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* ========================================
   3. 通用按钮样式系统
   ======================================== */

/* 基础按钮样式 */
.btn-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 8px 16px;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  white-space: nowrap;
  min-height: 36px;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}

/* 主要按钮 - 蓝色 */
.btn-blue {
  @extend .btn-base;
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.btn-blue:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
  transform: translateY(-1px);
}

/* 成功按钮 - 绿色 */
.btn-green {
  @extend .btn-base;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.btn-green:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
  transform: translateY(-1px);
}

/* 警告按钮 - 橙色 */
.btn-orange {
  @extend .btn-base;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
}

.btn-orange:hover {
  background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
  transform: translateY(-1px);
}

/* 危险按钮 - 红色 */
.btn-red {
  @extend .btn-base;
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

.btn-red:hover {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
  transform: translateY(-1px);
}

/* 次要按钮 - 灰色 */
.btn-gray {
  @extend .btn-base;
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(107, 114, 128, 0.3);
}

.btn-gray:hover {
  background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
  box-shadow: 0 4px 12px rgba(107, 114, 128, 0.4);
  transform: translateY(-1px);
}

/* 轻量按钮样式 */
.btn-blue-light {
  @extend .btn-base;
  background: rgba(59, 130, 246, 0.1);
  color: #2563eb;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.btn-blue-light:hover {
  background: rgba(59, 130, 246, 0.15);
  border-color: rgba(59, 130, 246, 0.3);
}

.btn-green-light {
  @extend .btn-base;
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.btn-green-light:hover {
  background: rgba(16, 185, 129, 0.15);
  border-color: rgba(16, 185, 129, 0.3);
}

/* 紧凑按钮 */
.btn-base.compact {
  padding: 4px 8px;
  font-size: 0.8rem;
  min-height: 28px;
}

/* 按钮禁用状态 */
.btn-base:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

/* ========================================
   4. Element Plus 弹窗样式系统
   ======================================== */

/* 弹窗基础样式 */
.el-dialog {
  max-width: 96vw !important;
  min-width: min(400px, 96vw) !important;
  width: max-content !important;
  box-sizing: border-box !important;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif !important;
}

/* 弹窗标题样式 */
.el-dialog__header .el-dialog__title {
  color: #1a1a2e !important;
  font-weight: 700 !important;
  font-size: 1.1rem !important;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif !important;
}

/* 弹窗主体样式 */
.el-dialog__body {
  color: #1a1a2e !important;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif !important;
}

/* 弹窗表单项样式 */
.el-dialog__body .el-form-item {
  display: flex !important;
  align-items: center !important;
  width: 100% !important;
  margin-bottom: 18px !important;
}

/* 弹窗表单标签样式 */
.el-dialog__body .el-form-item__label {
  color: #1a1a2e !important;
  font-weight: 700 !important;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif !important;
  font-size: 0.95rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: flex-start !important;
  font-variant-ligatures: none !important;
  flex: 0 0 120px !important;
  width: 120px !important;
  min-width: 80px !important;
  max-width: 180px !important;
  text-align: left !important;
  white-space: pre !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  padding-left: 0 !important;
  margin-right: 12px !important;
}

/* 弹窗表单内容区域 */
.el-dialog__body .el-form-item__content {
  flex: 1 1 0% !important;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif !important;
}

/* 弹窗输入框和文本域样式 */
.el-dialog__body .el-input__inner,
.el-dialog__body .el-textarea__inner {
  color: #1a1a2e !important;
  font-weight: 700 !important;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif !important;
  font-size: 0.9rem !important;
}

/* 弹窗占位符样式 */
.el-dialog__body .el-input__inner::placeholder,
.el-dialog__body .el-textarea__inner::placeholder {
  color: #6b7280 !important;
  font-weight: 600 !important;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif !important;
}

/* 弹窗下拉选择器样式 */
.el-dialog__body .el-select .el-input__inner {
  color: #1a1a2e !important;
  font-weight: 700 !important;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif !important;
  font-size: 0.9rem !important;
}

.el-dialog__body .el-select-dropdown .el-select-dropdown__item {
  color: #1a1a2e !important;
  font-weight: 700 !important;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif !important;
  font-size: 0.9rem !important;
}

/* 弹窗按钮样式 */
.el-dialog__footer .btn-blue,
.el-dialog__footer .btn-gray,
.el-dialog__footer button {
  font-weight: 700 !important;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif !important;
  font-size: 0.9rem !important;
}

/* 弹窗日期选择器样式 */
.el-dialog__body .el-date-editor .el-input__inner {
  color: #1a1a2e !important;
  font-weight: 700 !important;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif !important;
  font-size: 0.9rem !important;
}

/* 弹窗数字输入框样式 */
.el-dialog__body .el-input[type="number"] .el-input__inner {
  color: #1a1a2e !important;
  font-weight: 700 !important;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif !important;
  font-size: 0.9rem !important;
}

/* 弹窗表单整体样式 */
.el-dialog .el-form {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif !important;
}

/* 强制弹窗内所有文字样式 */
.el-dialog * {
  font-weight: 700 !important;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif !important;
}

.el-dialog input,
.el-dialog textarea,
.el-dialog .el-input__inner,
.el-dialog .el-textarea__inner {
  color: #1a1a2e !important;
  font-weight: 700 !important;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif !important;
}

.el-dialog .el-select .el-input__inner {
  color: #1a1a2e !important;
  font-weight: 700 !important;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif !important;
}

.el-dialog .el-select-dropdown__item {
  color: #1a1a2e !important;
  font-weight: 700 !important;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif !important;
}

/* 强制覆盖弹窗标签字体 - 最高优先级 */
.el-dialog .el-dialog__body .el-form-item__label,
.el-dialog__body .el-form-item__label,
body .el-dialog .el-dialog__body .el-form-item__label {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif !important;
  font-weight: 700 !important;
  color: #1a1a2e !important;
}

/* ========================================
   5. Element Plus 表格样式系统
   ======================================== */

/* 表格基础样式 */
.el-table {
  --el-table-border-color: #e5e7eb !important;
  --el-table-border: 1px solid #e5e7eb !important;
  --el-table-text-color: #374151 !important;
  --el-table-header-text-color: #1f2937 !important;
  --el-table-row-hover-bg-color: #f3f4f6 !important;
  --el-table-header-bg-color: #f9fafb !important;
  --el-table-fixed-box-shadow: 0 0 10px rgba(0, 0, 0, 0.1) !important;
  --el-table-bg-color: #ffffff !important;
  --el-table-tr-bg-color: #ffffff !important;
  --el-table-expanded-cell-bg-color: #f9fafb !important;

  width: 100% !important;
  table-layout: auto !important;
  font-size: 0.98rem !important;
  border-radius: 8px !important;
  margin-top: 16px !important;
  margin-bottom: 16px !important;
  border: 1px solid #e5e7eb !important;
  box-shadow: var(--shadow-sm) !important;
  color: #1a1a2e !important;
}

/* 表格头部样式 */
.el-table__header,
.erp-table .el-table__header {
  background-color: #f9fafb !important;
  width: 100% !important;
  table-layout: auto !important;
}

.el-table__header th.el-table__cell,
.erp-table .el-table__header th.el-table__cell {
  background-color: #f9fafb !important;
  border-bottom: 2px solid #e5e7eb !important;
  color: #1f2937 !important;
  font-weight: 600 !important;
  padding: 12px 8px !important;
  text-align: left !important;
}

/* 表格单元格样式 */
.el-table__cell,
.erp-table .el-table__cell {
  padding: 12px 8px !important;
  border-bottom: 1px solid #e5e7eb !important;
  vertical-align: middle !important;
  box-sizing: border-box !important;
  text-align: left !important;
  word-break: break-word !important;
  text-overflow: ellipsis !important;
}

/* 表格行样式 */
.el-table__row {
  height: 48px !important;
}

/* 表格悬停效果 */
.el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
  background-color: #f3f4f6 !important;
}

/* 表格边框样式 */
.el-table--border,
.el-table--group,
.el-table--border::after,
.el-table--group::after,
.el-table::before {
  background-color: #e5e7eb !important;
}

/* 表格固定列样式 */
.el-table .el-table__fixed,
.el-table .el-table__fixed-right {
  box-shadow: var(--shadow-sm) !important;
  z-index: 2 !important;
  background-color: white !important;
  height: 100% !important;
}

.el-table .el-table__fixed-right::before,
.el-table .el-table__fixed::before {
  background-color: #e5e7eb !important;
}

/* 表格容器样式 */
.erp-table-container {
  border-radius: 8px;
  overflow: hidden;
  background: white;
  box-shadow: var(--shadow-sm);
  margin-bottom: 20px;
  border: 1px solid #e5e7eb;
}

/* 表格工具栏样式 */
.erp-table-toolbar {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  background: white;
  border-bottom: 1px solid #e5e7eb;
}

.erp-table-toolbar .first-row {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.erp-table-toolbar .second-row {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

/* 表格内操作按钮 */
.action-buttons {
  display: flex !important;
  gap: 6px !important;
  flex-wrap: nowrap !important;
  min-width: fit-content !important;
}

/* ========================================
   6. 表单和输入组件样式
   ======================================== */

/* Element Plus 输入框样式 */
.el-input {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}

.el-input__inner {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  border-radius: 6px;
  border: 1px solid #d1d5db;
  transition: all 0.3s ease;
}

.el-input__inner:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Element Plus 选择器样式 */
.el-select {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}

.el-select--small .el-select__wrapper {
  min-width: 120px !important;
  max-width: 220px !important;
  width: auto !important;
  display: flex !important;
  align-items: center !important;
  box-sizing: border-box !important;
  min-height: 36px !important;
}

/* Element Plus 日期选择器样式 */
.el-date-picker {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}

/* 月份选择器特殊样式 */
.erp-table-toolbar .first-row .el-date-picker[placeholder*="选择月份"] {
  flex: 0 0 auto !important;
  min-width: 120px !important;
  max-width: 140px !important;
}

/* ========================================
   7. 布局和容器样式
   ======================================== */

/* 主容器样式 */
.erp-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 内容区域样式 */
.erp-content {
  flex: 1;
  padding: 20px;
  background: var(--bg-primary);
  min-height: calc(100vh - 60px);
}

/* 卡片容器样式 */
.erp-card {
  background: var(--bg-card);
  border-radius: var(--erp-border-radius);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.erp-card:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--border-hover);
}

/* 页面标题样式 */
.page-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-title i {
  color: var(--erp-primary);
}

/* 分割线样式 */
.divider {
  height: 1px;
  background: var(--border-color);
  margin: 20px 0;
}

/* ========================================
   8. 工具类样式
   ======================================== */

/* Flexbox 工具类 */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-end {
  justify-content: flex-end;
}

.flex-1 {
  flex: 1;
}

.flex-wrap {
  flex-wrap: wrap;
}

/* 间距工具类 */
.gap-2 {
  gap: 8px;
}

.gap-3 {
  gap: 12px;
}

.gap-4 {
  gap: 16px;
}

.space-x-2 > * + * {
  margin-left: 8px;
}

.space-x-4 > * + * {
  margin-left: 16px;
}

.space-y-2 > * + * {
  margin-top: 8px;
}

.space-y-4 > * + * {
  margin-top: 16px;
}

/* 文字工具类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.font-bold {
  font-weight: 700;
}

.font-semibold {
  font-weight: 600;
}

.font-medium {
  font-weight: 500;
}

/* 颜色工具类 */
.text-primary {
  color: var(--text-primary);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-success {
  color: var(--erp-success);
}

.text-warning {
  color: var(--erp-warning);
}

.text-danger {
  color: var(--erp-danger);
}

/* 背景工具类 */
.bg-white {
  background-color: white;
}

.bg-gray-50 {
  background-color: #f9fafb;
}

.bg-gray-100 {
  background-color: #f3f4f6;
}

/* 边框工具类 */
.border {
  border: 1px solid var(--border-color);
}

.border-t {
  border-top: 1px solid var(--border-color);
}

.border-b {
  border-bottom: 1px solid var(--border-color);
}

.border-l {
  border-left: 1px solid var(--border-color);
}

.border-r {
  border-right: 1px solid var(--border-color);
}

.rounded {
  border-radius: 6px;
}

.rounded-lg {
  border-radius: 8px;
}

.rounded-xl {
  border-radius: 12px;
}

/* 阴影工具类 */
.shadow-sm {
  box-shadow: var(--shadow-sm);
}

.shadow-md {
  box-shadow: var(--shadow-md);
}

.shadow-lg {
  box-shadow: var(--shadow-lg);
}

/* ========================================
   9. 响应式设计
   ======================================== */

/* 平板设备 */
@media (max-width: 1024px) {
  /* 弹窗响应式 */
  .el-dialog {
    min-width: 0 !important;
    max-width: 98vw !important;
    width: 98vw !important;
    padding: 0 4px !important;
  }

  .el-dialog__body .el-form-item__label {
    flex: 0 0 90px !important;
    width: 90px !important;
    min-width: 60px !important;
    max-width: 120px !important;
    font-size: 0.98rem !important;
  }

  /* 表格响应式 */
  .el-table .el-table__cell {
    padding: 6px 4px !important;
    font-size: 0.9rem !important;
  }

  /* 按钮响应式 */
  .btn-blue-light.compact,
  .btn-green-light.compact {
    padding: 3px 6px !important;
    font-size: 0.8rem !important;
    min-width: 50px !important;
    height: 26px !important;
  }

  /* 内容区域响应式 */
  .erp-content {
    padding: 12px;
  }
}

/* 手机设备 */
@media (max-width: 640px) {
  /* 弹窗响应式 */
  .el-dialog__body .el-form-item {
    flex-direction: column !important;
    align-items: flex-start !important;
    margin-bottom: 12px !important;
  }

  .el-dialog__body .el-form-item__label {
    width: 100% !important;
    min-width: 0 !important;
    max-width: 100% !important;
    text-align: left !important;
    margin-bottom: 4px !important;
  }

  /* 表格工具栏响应式 */
  .erp-table-toolbar .first-row {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  /* 按钮响应式 */
  .btn-base {
    width: 100%;
    justify-content: center;
  }

  /* 内容区域响应式 */
  .erp-content {
    padding: 8px;
  }
}

/* 超小屏幕设备 */
@media (max-width: 480px) {
  .el-dialog__body .el-form-item {
    margin-bottom: 8px !important;
  }

  .el-dialog__body .el-form-item__label {
    width: 100% !important;
    min-width: 0 !important;
    max-width: 100% !important;
    text-align: left !important;
    margin-bottom: 2px !important;
  }
}

/* ========================================
   10. 动画和过渡效果
   ======================================== */

/* 通用过渡效果 */
.transition-all {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-colors {
  transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
}

.transition-transform {
  transition: transform 0.3s ease;
}

/* 悬停动画 */
.hover-lift:hover {
  transform: translateY(-2px);
}

.hover-scale:hover {
  transform: scale(1.02);
}

/* 淡入动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* 滑入动画 */
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

/* 脉冲动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.pulse {
  animation: pulse 2s infinite;
}

/* ========================================
   11. 特殊组件样式
   ======================================== */

/* 加载状态样式 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(2px);
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid var(--erp-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 状态标签样式 */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-badge.success {
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.status-badge.warning {
  background: rgba(245, 158, 11, 0.1);
  color: #d97706;
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.status-badge.danger {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.status-badge.info {
  background: rgba(59, 130, 246, 0.1);
  color: #2563eb;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.status-badge.neutral {
  background: rgba(107, 114, 128, 0.1);
  color: #4b5563;
  border: 1px solid rgba(107, 114, 128, 0.2);
}

/* 通知样式 */
.notification {
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 500;
}

.notification.success {
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
  border-left: 4px solid #10b981;
}

.notification.warning {
  background: rgba(245, 158, 11, 0.1);
  color: #d97706;
  border-left: 4px solid #f59e0b;
}

.notification.error {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border-left: 4px solid #ef4444;
}

.notification.info {
  background: rgba(59, 130, 246, 0.1);
  color: #2563eb;
  border-left: 4px solid #3b82f6;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--text-tertiary);
}

.empty-state i {
  font-size: 3rem;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--text-secondary);
}

.empty-state p {
  font-size: 0.9rem;
  margin-bottom: 20px;
}

/* ========================================
   12. 打印样式
   ======================================== */

@media print {
  /* 隐藏不需要打印的元素 */
  .no-print,
  .btn-base,
  .erp-table-toolbar,
  .el-dialog,
  .loading-overlay {
    display: none !important;
  }

  /* 打印时的表格样式 */
  .el-table {
    border: 1px solid #000 !important;
    font-size: 12px !important;
  }

  .el-table .el-table__cell {
    border: 1px solid #000 !important;
    padding: 4px !important;
  }

  /* 打印时的页面样式 */
  body {
    background: white !important;
    color: black !important;
  }

  .erp-content {
    padding: 0 !important;
  }
}

/* ========================================
   13. 辅助功能和可访问性
   ======================================== */

/* 焦点样式 */
.btn-base:focus,
.el-input__inner:focus,
.el-select:focus {
  outline: 2px solid var(--erp-primary);
  outline-offset: 2px;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  :root {
    --text-primary: #000000;
    --text-secondary: #333333;
    --border-color: #666666;
    --bg-card: #ffffff;
  }

  .btn-base {
    border: 2px solid currentColor;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 屏幕阅读器专用样式 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
