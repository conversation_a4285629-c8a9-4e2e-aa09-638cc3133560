# ERP系统全局CSS样式文件结构说明

## 📁 文件概览

- **原文件**: `global-theme.css` (3570行，结构混乱)
- **整理后**: `global-theme-organized.css` (1243行，结构清晰)

## 🗂️ 新的文件结构

### 1. CSS变量定义 - 支持明暗模式切换
- **浅色模式变量** (`:root`)
  - 背景色系 (`--bg-*`)
  - 文字色系 (`--text-*`)
  - 边框色系 (`--border-*`)
  - 主题色系 (`--accent-*`)
  - 阴影系统 (`--shadow-*`)
  - 玻璃拟态效果 (`--glass-*`)
  - 发光效果 (`--glow-*`)
  - ERP系统专用色彩 (`--erp-*`)

- **深色模式变量** (`[data-theme="dark"]`)
  - 对应浅色模式的所有变量
  - 适配深色主题的颜色值

### 2. 全局基础样式
- **全局样式**: `body` 元素基础设置
- **全局重置**: 通用重置规则
- **滚动条样式**: 自定义滚动条外观

### 3. 通用按钮样式系统
- **基础按钮**: `.btn-base` 通用按钮样式
- **主题按钮**: 
  - `.btn-blue` (主要按钮 - 蓝色)
  - `.btn-green` (成功按钮 - 绿色)
  - `.btn-orange` (警告按钮 - 橙色)
  - `.btn-red` (危险按钮 - 红色)
  - `.btn-gray` (次要按钮 - 灰色)
- **轻量按钮**: `.btn-*-light` 系列
- **紧凑按钮**: `.compact` 修饰符
- **按钮状态**: `:hover`, `:disabled` 等状态样式

### 4. Element Plus 弹窗样式系统
- **弹窗基础样式**: `.el-dialog` 容器样式
- **弹窗标题**: `.el-dialog__header .el-dialog__title`
- **弹窗主体**: `.el-dialog__body`
- **表单组件样式**:
  - 表单项 (`.el-form-item`)
  - 表单标签 (`.el-form-item__label`)
  - 输入框 (`.el-input__inner`)
  - 下拉选择器 (`.el-select`)
  - 日期选择器 (`.el-date-editor`)
  - 数字输入框
- **强制覆盖规则**: 确保字体一致性的最高优先级规则

### 5. Element Plus 表格样式系统
- **表格基础样式**: `.el-table` 及CSS变量设置
- **表格头部**: `.el-table__header` 样式
- **表格单元格**: `.el-table__cell` 样式
- **表格行**: `.el-table__row` 样式
- **悬停效果**: 行悬停状态
- **边框样式**: 表格边框设置
- **固定列**: 固定列阴影效果
- **表格容器**: `.erp-table-container` 包装器
- **表格工具栏**: `.erp-table-toolbar` 操作区域
- **操作按钮**: `.action-buttons` 表格内按钮组

### 6. 表单和输入组件样式
- **Element Plus 输入框**: `.el-input` 系列
- **Element Plus 选择器**: `.el-select` 系列
- **Element Plus 日期选择器**: `.el-date-picker` 系列
- **特殊组件**: 月份选择器等定制样式

### 7. 布局和容器样式
- **主容器**: `.erp-container` 主布局容器
- **内容区域**: `.erp-content` 内容区域
- **卡片容器**: `.erp-card` 卡片样式
- **页面标题**: `.page-title` 标题样式
- **分割线**: `.divider` 分割线样式

### 8. 工具类样式
- **Flexbox 工具类**: `.flex`, `.flex-col`, `.items-center` 等
- **间距工具类**: `.gap-*`, `.space-*` 系列
- **文字工具类**: `.text-*`, `.font-*` 系列
- **颜色工具类**: `.text-primary`, `.bg-*` 系列
- **边框工具类**: `.border`, `.rounded` 系列
- **阴影工具类**: `.shadow-*` 系列

### 9. 响应式设计
- **平板设备** (`@media (max-width: 1024px)`)
  - 弹窗响应式调整
  - 表格响应式调整
  - 按钮响应式调整
- **手机设备** (`@media (max-width: 640px)`)
  - 弹窗垂直布局
  - 表格工具栏调整
  - 按钮全宽显示
- **超小屏幕** (`@media (max-width: 480px)`)
  - 进一步的间距优化

### 10. 动画和过渡效果
- **通用过渡**: `.transition-*` 系列
- **悬停动画**: `.hover-*` 系列
- **关键帧动画**: `@keyframes` 定义
  - `fadeIn` (淡入)
  - `slideInRight` (滑入)
  - `pulse` (脉冲)
  - `spin` (旋转)

### 11. 特殊组件样式
- **加载状态**: `.loading-overlay`, `.loading-spinner`
- **状态标签**: `.status-badge` 及其变体
- **通知样式**: `.notification` 及其类型
- **空状态**: `.empty-state` 空数据显示

### 12. 打印样式
- **打印媒体查询**: `@media print`
- **隐藏元素**: 不需要打印的组件
- **打印优化**: 表格和页面的打印样式

### 13. 辅助功能和可访问性
- **焦点样式**: 键盘导航支持
- **高对比度模式**: `@media (prefers-contrast: high)`
- **减少动画模式**: `@media (prefers-reduced-motion: reduce)`
- **屏幕阅读器**: `.sr-only` 辅助类

## 🎯 整理的优势

### 1. **结构清晰**
- 按功能模块分组
- 每个区域有明确的注释标识
- 相关样式集中管理

### 2. **易于维护**
- 快速定位需要修改的样式
- 避免重复定义
- 统一的命名规范

### 3. **性能优化**
- 移除重复的CSS规则
- 优化选择器优先级
- 减少文件大小 (3570行 → 1243行)

### 4. **扩展性强**
- 模块化设计便于添加新功能
- CSS变量系统支持主题切换
- 响应式设计覆盖多种设备

### 5. **代码质量**
- 统一的字体系统
- 一致的颜色方案
- 标准化的组件样式

## 📝 使用建议

1. **替换原文件**: 将 `global-theme-organized.css` 替换 `global-theme.css`
2. **更新引用**: 确保所有页面正确引用新的CSS文件
3. **测试验证**: 在各个页面测试样式是否正常
4. **持续维护**: 新增样式时按照现有结构添加到对应区域

## 🔧 字体一致性修复

特别解决了弹窗字体不一致的问题：
- **统一字体系列**: `'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif`
- **强制覆盖规则**: 确保弹窗中所有文字使用正确字体
- **优先级管理**: 使用 `!important` 和高优先级选择器确保样式生效
