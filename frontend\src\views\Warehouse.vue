<template>
  <ErpContent v-model="activeTab">
    <template #stats>
      <div class="erp-stat-card">
        <div class="erp-stat-label">当前库存总量</div>
        <div class="erp-stat-value">1200</div>
        <i class="fas fa-boxes erp-stat-icon blue"></i>
      </div>
      <div class="erp-stat-card">
        <div class="erp-stat-label">本月入库单数</div>
        <div class="erp-stat-value">15</div>
        <i class="fas fa-arrow-down erp-stat-icon green"></i>
      </div>
      <div class="erp-stat-card">
        <div class="erp-stat-label">本月出库单数</div>
        <div class="erp-stat-value">10</div>
        <i class="fas fa-arrow-up erp-stat-icon purple"></i>
      </div>
      <div class="erp-stat-card">
        <div class="erp-stat-label">操作</div>
        <button class="btn-blue" @click="addInbound">
          <i class="fas fa-plus-circle"></i>
          <span>新建入库</span>
        </button>
      </div>
    </template>
    <template #search>
      <!-- 保留一个空的search插槽，因为我们将搜索栏移到toolbar中 -->
    </template>
    <template #toolbar>
      <div class="erp-table-toolbar">
        <div class="first-row">
          <el-input v-model="search.keyword" placeholder="输入物料编码/名称搜索" size="small" clearable />
          <button class="btn-blue" @click="searchItems">
            <i class="fas fa-search"></i>
            <span>搜索</span>
          </button>
          <button class="btn-gray" @click="resetSearch">
            <i class="fas fa-undo"></i>
            <span>重置</span>
          </button>
        </div>
      </div>
    </template>
    <template #tabs>
      <el-tabs v-model="activeTab">
        <el-tab-pane label="库存管理" name="materials">
          <ErpTable
            ref="materialsTable"
            :data="filteredMaterialsList"
            :pagination="true"
            :page-size="20"
            max-height="600"
            style="width: 100%"
            table-layout="auto"
          >
            <el-table-column prop="code" label="物料编码" />
            <el-table-column prop="name" label="物料名称" />
            <el-table-column prop="category" label="类别" />
            <el-table-column prop="quantity" label="库存数量">
              <template #default="scope">
                <span :class="getQuantityClass(scope.row.quantity)">
                  {{ scope.row.quantity.toLocaleString('zh-CN') }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="location" label="库位" />
            <el-table-column prop="lastInbound" label="最近入库" />
            <el-table-column label="操作">
              <template #default="scope">
                <div class="action-buttons">
                  <button class="btn-blue-light compact" @click="viewMaterial(scope.row)">
                    <i class="fas fa-eye"></i>
                    <span>查看</span>
                  </button>
                  <button class="btn-green-light compact" @click="inboundMaterial(scope.row)">
                    <i class="fas fa-arrow-down"></i>
                    <span>入库</span>
                  </button>
                  <button class="btn-orange-light compact" @click="outboundMaterial(scope.row)">
                    <i class="fas fa-arrow-up"></i>
                    <span>出库</span>
                  </button>
                  <button class="btn-yellow-light compact" @click="editMaterial(scope.row)">
                    <i class="fas fa-edit"></i>
                    <span>编辑</span>
                  </button>
                </div>
              </template>
            </el-table-column>
          </ErpTable>
        </el-tab-pane>
        <el-tab-pane label="入库记录" name="inbound">
          <ErpTable
            ref="inboundTable"
            :data="filteredInboundRecords"
            :pagination="true"
            :page-size="10"
            max-height="600"
            style="width: 100%"
            table-layout="auto"
          >
            <el-table-column prop="order" label="入库单号" />
            <el-table-column prop="product" label="物料名称" />
            <el-table-column prop="quantity" label="入库数量">
              <template #default="scope">
                <span class="quantity-inbound">
                  +{{ scope.row.quantity.toLocaleString('zh-CN') }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="date" label="入库日期" />
            <el-table-column prop="operator" label="操作员" />
            <el-table-column prop="supplier" label="供应商" />
            <el-table-column label="操作">
              <template #default="scope">
                <div class="action-buttons">
                  <button class="btn-blue-light compact" @click="viewRecord(scope.row)">
                    <i class="fas fa-eye"></i>
                    <span>查看</span>
                  </button>
                  <button class="btn-yellow-light compact" @click="editRecord(scope.row)">
                    <i class="fas fa-edit"></i>
                    <span>编辑</span>
                  </button>
                </div>
              </template>
            </el-table-column>
          </ErpTable>
        </el-tab-pane>
        <el-tab-pane label="出库记录" name="outbound">
          <ErpTable
            ref="outboundTable"
            :data="filteredOutboundRecords"
            :pagination="true"
            :page-size="10"
            max-height="600"
            style="width: 100%"
            table-layout="auto"
          >
            <el-table-column prop="order" label="出库单号" />
            <el-table-column prop="product" label="物料名称" />
            <el-table-column prop="quantity" label="出库数量">
              <template #default="scope">
                <span class="quantity-outbound">
                  -{{ scope.row.quantity.toLocaleString('zh-CN') }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="date" label="出库日期" />
            <el-table-column prop="operator" label="操作员" />
            <el-table-column prop="department" label="领用部门" />
            <el-table-column label="操作">
              <template #default="scope">
                <div class="action-buttons">
                  <button class="btn-blue-light compact" @click="viewRecord(scope.row)">
                    <i class="fas fa-eye"></i>
                    <span>查看</span>
                  </button>
                  <button class="btn-yellow-light compact" @click="editRecord(scope.row)">
                    <i class="fas fa-edit"></i>
                    <span>编辑</span>
                  </button>
                </div>
              </template>
            </el-table-column>
          </ErpTable>
        </el-tab-pane>
      </el-tabs>
    </template>
  </ErpContent>

  <!-- 新建/编辑入库单弹窗 -->
  <el-dialog v-model="dialogVisible" :title="dialogMode === 'add' ? '新建入库单' : '编辑入库单'" width="600px">
    <el-form :model="form" label-width="100px">
      <el-form-item label="入库单号">
        <el-input v-model="form.orderNo" placeholder="自动生成" :disabled="dialogMode === 'add'" />
      </el-form-item>
      <el-form-item label="物料名称">
        <el-select v-model="form.materialCode" placeholder="请选择物料" style="width: 100%" @change="onMaterialChange">
          <el-option
            v-for="material in materialsList"
            :key="material.code"
            :label="`${material.name} (${material.code})`"
            :value="material.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="入库数量">
        <el-input v-model="form.quantity" type="number" placeholder="请输入入库数量" />
      </el-form-item>
      <el-form-item label="供应商">
        <el-input v-model="form.supplier" placeholder="请输入供应商名称" />
      </el-form-item>
      <el-form-item label="入库日期">
        <el-date-picker
          v-model="form.date"
          type="date"
          placeholder="选择入库日期"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="操作员">
        <el-input v-model="form.operator" placeholder="请输入操作员姓名" />
      </el-form-item>
      <el-form-item label="备注">
        <el-input v-model="form.remark" type="textarea" placeholder="请输入备注信息" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="flex justify-end space-x-4">
        <button class="btn-gray" @click="dialogVisible = false">
          <i class="fas fa-times"></i>
          <span>取消</span>
        </button>
        <button class="btn-blue" @click="saveInbound">
          <i class="fas fa-save"></i>
          <span>保存</span>
        </button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, watch } from 'vue'
import ErpContent from '../components/ErpContent.vue'
import ErpTable from '../components/common/ErpTable.vue'

// 定义入库单表单数据类型
interface InboundForm {
  id: number
  orderNo: string
  materialCode: string
  materialName: string
  quantity: number | string
  supplier: string
  date: string
  operator: string
  remark: string
}

const activeTab = ref('materials')

// 表格引用
const materialsTable = ref()
const inboundTable = ref()
const outboundTable = ref()

// 弹窗相关状态
const dialogVisible = ref(false)
const dialogMode = ref<'add' | 'edit'>('add')
const form = ref<InboundForm>({
  id: 0,
  orderNo: '',
  materialCode: '',
  materialName: '',
  quantity: '',
  supplier: '',
  date: '',
  operator: '',
  remark: ''
})

// 库存物料数据
const materialsList = ref([
  { code: 'A001', name: '高强度钢材', category: '原材料', spec: '100kg/卷', unit: 'kg', quantity: 1200, location: 'A区-01', lastInbound: '2024-06-01', lastOutbound: '2024-06-02' },
  { code: 'A002', name: '精密轴承', category: '零部件', spec: '直径50mm', unit: '个', quantity: 850, location: 'A区-02', lastInbound: '2024-06-02', lastOutbound: '2024-06-03' },
  { code: 'A003', name: '不锈钢板材', category: '原材料', spec: '2mm厚', unit: '张', quantity: 320, location: 'A区-03', lastInbound: '2024-06-03', lastOutbound: '2024-06-04' },
  { code: 'A004', name: '铝合金型材', category: '原材料', spec: '30x30mm', unit: '米', quantity: 750, location: 'A区-04', lastInbound: '2024-06-04', lastOutbound: '2024-06-05' },
  { code: 'A005', name: '铜导线', category: '原材料', spec: '2.5mm²', unit: '米', quantity: 1500, location: 'A区-05', lastInbound: '2024-06-05', lastOutbound: '2024-06-06' },
  { code: 'A006', name: '碳纤维板', category: '原材料', spec: '3mm厚', unit: '张', quantity: 80, location: 'A区-06', lastInbound: '2024-06-06', lastOutbound: '2024-06-07' },
  { code: 'B001', name: '电机控制器', category: '电子元件', spec: '220V/50Hz', unit: '台', quantity: 320, location: 'B区-01', lastInbound: '2024-06-03', lastOutbound: '2024-06-04' },
  { code: 'B002', name: '传感器模块', category: '电子元件', spec: '温度传感器', unit: '个', quantity: 450, location: 'B区-02', lastInbound: '2024-06-04', lastOutbound: '2024-06-05' },
  { code: 'B003', name: 'PLC控制器', category: '电子元件', spec: '16点位', unit: '台', quantity: 120, location: 'B区-03', lastInbound: '2024-06-05', lastOutbound: '2024-06-06' },
  { code: 'B004', name: '触摸屏', category: '电子元件', spec: '10.1英寸', unit: '台', quantity: 75, location: 'B区-04', lastInbound: '2024-06-06', lastOutbound: '2024-06-07' },
  { code: 'B005', name: '变频器', category: '电子元件', spec: '7.5kW', unit: '台', quantity: 45, location: 'B区-05', lastInbound: '2024-06-07', lastOutbound: '2024-06-08' },
  { code: 'B006', name: '继电器', category: '电子元件', spec: '24V', unit: '个', quantity: 680, location: 'B区-06', lastInbound: '2024-06-08', lastOutbound: '2024-06-09' },
  { code: 'C001', name: '液压油缸', category: '液压件', spec: '缸径100mm', unit: '个', quantity: 180, location: 'C区-01', lastInbound: '2024-06-05', lastOutbound: '2024-06-06' },
  { code: 'C002', name: '密封圈套装', category: '密封件', spec: '耐高温橡胶', unit: '套', quantity: 2500, location: 'C区-02', lastInbound: '2024-06-06', lastOutbound: '2024-06-07' },
  { code: 'C003', name: '气动阀门', category: '气动件', spec: 'DN50', unit: '个', quantity: 95, location: 'C区-03', lastInbound: '2024-06-07', lastOutbound: '2024-06-08' },
  { code: 'C004', name: '液压泵', category: '液压件', spec: '25MPa', unit: '台', quantity: 30, location: 'C区-04', lastInbound: '2024-06-08', lastOutbound: '2024-06-09' },
  { code: 'C005', name: '气动接头', category: '气动件', spec: '快插式', unit: '个', quantity: 1200, location: 'C区-05', lastInbound: '2024-06-09', lastOutbound: '2024-06-10' },
  { code: 'C006', name: '液压油', category: '液压件', spec: '46#', unit: '升', quantity: 800, location: 'C区-06', lastInbound: '2024-06-10', lastOutbound: '2024-06-11' },
  { code: 'D001', name: '螺栓', category: '标准件', spec: 'M8x30', unit: '个', quantity: 5000, location: 'D区-01', lastInbound: '2024-06-11', lastOutbound: '2024-06-12' },
  { code: 'D002', name: '螺母', category: '标准件', spec: 'M8', unit: '个', quantity: 5200, location: 'D区-02', lastInbound: '2024-06-12', lastOutbound: '2024-06-13' },
  { code: 'D003', name: '垫片', category: '标准件', spec: '8mm', unit: '个', quantity: 4800, location: 'D区-03', lastInbound: '2024-06-13', lastOutbound: '2024-06-14' },
  { code: 'D004', name: '弹簧垫圈', category: '标准件', spec: '8mm', unit: '个', quantity: 3500, location: 'D区-04', lastInbound: '2024-06-14', lastOutbound: '2024-06-15' },
  { code: 'D005', name: '内六角扳手', category: '工具', spec: '3mm', unit: '把', quantity: 120, location: 'D区-05', lastInbound: '2024-06-15', lastOutbound: '2024-06-16' },
  { code: 'D006', name: '电动扳手', category: '工具', spec: '18V', unit: '把', quantity: 25, location: 'D区-06', lastInbound: '2024-06-16', lastOutbound: '2024-06-17' }
])

// 入库记录数据
const inboundRecords = ref([
  { order: 'IN-2024-001', product: '高强度钢材', quantity: 500, date: '2024-06-01', operator: '张三', supplier: '钢材供应商A' },
  { order: 'IN-2024-002', product: '精密轴承', quantity: 200, date: '2024-06-02', operator: '王五', supplier: '轴承制造商B' },
  { order: 'IN-2024-003', product: '电机控制器', quantity: 50, date: '2024-06-03', operator: '张三', supplier: '电子设备公司C' },
  { order: 'IN-2024-004', product: '传感器模块', quantity: 100, date: '2024-06-04', operator: '王五', supplier: '传感器科技D' },
  { order: 'IN-2024-005', product: '液压油缸', quantity: 30, date: '2024-06-05', operator: '李四', supplier: '液压设备厂E' },
  { order: 'IN-2024-006', product: '密封圈套装', quantity: 1000, date: '2024-06-06', operator: '张三', supplier: '密封件厂F' },
  { order: 'IN-2024-007', product: '不锈钢板材', quantity: 150, date: '2024-06-07', operator: '王五', supplier: '钢材供应商A' },
  { order: 'IN-2024-008', product: '铝合金型材', quantity: 300, date: '2024-06-08', operator: '李四', supplier: '金属材料厂G' },
  { order: 'IN-2024-009', product: '铜导线', quantity: 800, date: '2024-06-09', operator: '张三', supplier: '电线电缆厂H' },
  { order: 'IN-2024-010', product: 'PLC控制器', quantity: 40, date: '2024-06-10', operator: '王五', supplier: '自动化设备公司I' },
  { order: 'IN-2024-011', product: '触摸屏', quantity: 25, date: '2024-06-11', operator: '李四', supplier: '显示设备厂J' },
  { order: 'IN-2024-012', product: '变频器', quantity: 15, date: '2024-06-12', operator: '张三', supplier: '电气设备厂K' },
  { order: 'IN-2024-013', product: '继电器', quantity: 200, date: '2024-06-13', operator: '王五', supplier: '电气元件厂L' },
  { order: 'IN-2024-014', product: '气动阀门', quantity: 35, date: '2024-06-14', operator: '李四', supplier: '气动设备厂M' },
  { order: 'IN-2024-015', product: '液压泵', quantity: 10, date: '2024-06-15', operator: '张三', supplier: '液压设备厂E' }
])

// 出库记录数据
const outboundRecords = ref([
  { order: 'OUT-2024-001', product: '高强度钢材', quantity: 300, date: '2024-06-02', operator: '李四', department: '生产部' },
  { order: 'OUT-2024-002', product: '精密轴承', quantity: 150, date: '2024-06-03', operator: '赵六', department: '装配车间' },
  { order: 'OUT-2024-003', product: '电机控制器', quantity: 25, date: '2024-06-04', operator: '李四', department: '电气部' },
  { order: 'OUT-2024-004', product: '传感器模块', quantity: 80, date: '2024-06-05', operator: '赵六', department: '测试部' },
  { order: 'OUT-2024-005', product: '密封圈套装', quantity: 500, date: '2024-06-06', operator: '张三', department: '维修部' },
  { order: 'OUT-2024-006', product: '不锈钢板材', quantity: 120, date: '2024-06-07', operator: '李四', department: '生产部' },
  { order: 'OUT-2024-007', product: '铝合金型材', quantity: 200, date: '2024-06-08', operator: '赵六', department: '装配车间' },
  { order: 'OUT-2024-008', product: '铜导线', quantity: 350, date: '2024-06-09', operator: '张三', department: '电气部' },
  { order: 'OUT-2024-009', product: 'PLC控制器', quantity: 15, date: '2024-06-10', operator: '李四', department: '自动化部' },
  { order: 'OUT-2024-010', product: '触摸屏', quantity: 8, date: '2024-06-11', operator: '赵六', department: '测试部' },
  { order: 'OUT-2024-011', product: '变频器', quantity: 5, date: '2024-06-12', operator: '张三', department: '电气部' },
  { order: 'OUT-2024-012', product: '继电器', quantity: 120, date: '2024-06-13', operator: '李四', department: '维修部' },
  { order: 'OUT-2024-013', product: '气动阀门', quantity: 18, date: '2024-06-14', operator: '赵六', department: '生产部' },
  { order: 'OUT-2024-014', product: '液压泵', quantity: 3, date: '2024-06-15', operator: '张三', department: '维修部' },
  { order: 'OUT-2024-015', product: '螺栓', quantity: 1000, date: '2024-06-16', operator: '李四', department: '装配车间' }
])
const search = ref({
  keyword: '',
  category: ''
})

// 通用过滤函数
const filterByKeyword = (data: any[], searchFields: string[]) => {
  if (!search.value.keyword) return data
  const keyword = search.value.keyword.toLowerCase()
  return data.filter(item =>
    searchFields.some(field =>
      item[field]?.toLowerCase().includes(keyword)
    )
  )
}

// 计算属性 - 过滤后的数据
const filteredMaterialsList = computed(() => {
  let filtered = filterByKeyword(materialsList.value, ['code', 'name'])

  // 类别筛选（仅适用于物料）
  if (search.value.category) {
    filtered = filtered.filter(item => item.category === search.value.category)
  }

  return filtered
})

const filteredInboundRecords = computed(() =>
  filterByKeyword(inboundRecords.value, ['order', 'product', 'supplier'])
)

const filteredOutboundRecords = computed(() =>
  filterByKeyword(outboundRecords.value, ['order', 'product', 'department'])
)

// 库存数量样式
function getQuantityClass(quantity: number) {
  if (quantity < 100) return 'quantity-low'
  if (quantity < 500) return 'quantity-medium'
  return 'quantity-high'
}

// 通用操作函数
const handleAction = (action: string, row?: any) => {
  console.log(`${action}:`, row || '新建操作')
  // TODO: 根据action类型打开对应的对话框
}

// 物料操作函数
function addInbound() {
  dialogMode.value = 'add'
  form.value = {
    id: 0,
    orderNo: generateInboundOrderNo(),
    materialCode: '',
    materialName: '',
    quantity: '',
    supplier: '',
    date: new Date().toISOString().split('T')[0],
    operator: '',
    remark: ''
  }
  dialogVisible.value = true
}

// 生成入库单号
function generateInboundOrderNo(): string {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
  return `IN-${year}${month}${day}-${random}`
}

// 物料选择变化时更新物料名称
function onMaterialChange(materialCode: string) {
  const material = materialsList.value.find(m => m.code === materialCode)
  if (material) {
    form.value.materialName = material.name
  }
}

// 保存入库单
function saveInbound() {
  if (!form.value.materialCode || !form.value.quantity || !form.value.supplier || !form.value.operator) {
    console.warn('请填写完整信息')
    return
  }

  const quantity = typeof form.value.quantity === 'string'
    ? parseInt(form.value.quantity)
    : form.value.quantity

  if (dialogMode.value === 'add') {
    // 新增入库记录
    const newRecord = {
      order: form.value.orderNo,
      product: form.value.materialName,
      quantity: quantity,
      date: form.value.date,
      operator: form.value.operator,
      supplier: form.value.supplier
    }
    inboundRecords.value.unshift(newRecord)

    // 更新库存数量
    const material = materialsList.value.find(m => m.code === form.value.materialCode)
    if (material) {
      material.quantity += quantity
      material.lastInbound = form.value.date
    }
  }

  dialogVisible.value = false
}

const viewMaterial = (row: any) => handleAction('查看物料详情', row)
const editMaterial = (row: any) => handleAction('编辑物料', row)
const inboundMaterial = (row: any) => handleAction('物料入库', row)
const outboundMaterial = (row: any) => handleAction('物料出库', row)

// 记录操作函数
const viewRecord = (row: any) => handleAction('查看出入库记录', row)
const editRecord = (row: any) => handleAction('编辑出入库记录', row)

// 搜索功能
const searchItems = () => console.log('搜索条件:', search.value) // 搜索逻辑已通过计算属性实现
const resetSearch = () => Object.assign(search.value, { keyword: '', category: '' })

// 表格引用映射
const tableRefs = {
  materials: materialsTable,
  inbound: inboundTable,
  outbound: outboundTable
}

// 重新初始化当前活动表格
const reinitializeCurrentTable = () => {
  nextTick(() => {
    const currentTable = tableRefs[activeTab.value as keyof typeof tableRefs]?.value
    if (currentTable?.tableRef) {
      setTimeout(() => window.dispatchEvent(new Event('resize')), 150)
    }
  })
}

// 监听标签页切换
watch(activeTab, reinitializeCurrentTable)
</script>

<style scoped>
/* 库存数量样式 */
.quantity-low {
  color: #f56565;
  font-weight: 600;
}

.quantity-medium {
  color: #ed8936;
  font-weight: 500;
}

.quantity-high {
  color: #38a169;
  font-weight: 500;
}

/* 入库出库数量样式 */
.quantity-inbound {
  color: #38a169;
  font-weight: 600;
}

.quantity-outbound {
  color: #e53e3e;
  font-weight: 600;
}
</style>