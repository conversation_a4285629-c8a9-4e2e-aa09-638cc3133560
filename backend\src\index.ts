import { Elysia } from 'elysia'
import jwt from 'jsonwebtoken'

const app = new Elysia()
  .get('/', () => 'Hello Elysia + Bun!')

const JWT_SECRET = 'your_secret_key' // 生产环境请用更安全的密钥

// 用户结构增加角色
const users: Record<string, { password: string; role: 'admin' | 'user' }> = {}

// 注册时可指定角色（默认user）
app.post('/api/register', async ({ body }) => {
  const { username, password, role } = body as { username: string; password: string; role?: 'admin' | 'user' }
  if (!username || !password) return { success: false, message: '用户名和密码必填' }
  if (users[username]) return { success: false, message: '用户已存在' }
  users[username] = { password, role: role || 'user' }
  return { success: true, message: '注册成功' }
})

// 登录时返回角色
app.post('/api/login', async ({ body }) => {
  const { username, password } = body as { username: string; password: string }
  const user = users[username]
  if (!user || user.password !== password) return { success: false, message: '用户名或密码错误' }
  const token = jwt.sign({ username, role: user.role }, JWT_SECRET, { expiresIn: '2h' })
  return { success: true, token, role: user.role }
})

// RBAC中间件
function requireRole(role: 'admin' | 'user') {
  return ({ headers }: any) => {
    const auth = headers['authorization'] || ''
    const token = auth.replace('Bearer ', '')
    try {
      const payload = jwt.verify(token, JWT_SECRET) as any
      if (payload.role !== role) return { success: false, message: '无权限' }
      return null // 通过
    } catch {
      return { success: false, message: '无效token' }
    }
  }
}

// 受保护API示例
app.get('/api/admin/only', requireRole('admin'), () => {
  return { message: '只有admin角色可以访问' }
})

app.listen(3000)

console.log('🚀 Elysia server running at http://localhost:3000') 