<template>
  <div class="erp-dashboard-page">
    <div class="erp-stats-row">
      <div class="erp-stat-card">
        <div class="erp-stat-label">本月新签合同</div>
        <div class="erp-stat-value">5</div>
        <i class="fas fa-file-contract erp-stat-icon blue"></i>
      </div>
      <div class="erp-stat-card">
        <div class="erp-stat-label">履约中合同</div>
        <div class="erp-stat-value">8</div>
        <i class="fas fa-tasks erp-stat-icon green"></i>
      </div>
      <div class="erp-stat-card">
        <div class="erp-stat-label">已归档合同</div>
        <div class="erp-stat-value">21</div>
        <i class="fas fa-archive erp-stat-icon purple"></i>
      </div>
      <div class="erp-stat-card">
        <div class="erp-stat-label">操作</div>
        <button class="btn-blue" @click="createContract">
          <i class="fas fa-plus-circle"></i>
          <span>新建合同</span>
        </button>
      </div>
    </div>
    <div class="erp-card">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="合同管理" name="contract">
          <ErpTable :data="contractList">
            <el-table-column prop="contractNo" label="合同编号" />
            <el-table-column prop="name" label="合同名称" />
            <el-table-column prop="partyA" label="甲方" />
            <el-table-column prop="partyB" label="乙方" />
            <el-table-column prop="amount" label="金额">
              <template #default="scope">
                ¥{{ scope.row.amount.toLocaleString('zh-CN') }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态">
              <template #default="scope">
                <span :class="getStatusClass(scope.row.status)">
                  {{ scope.row.status }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="date" label="日期" />
            <el-table-column label="操作">
              <template #default="scope">
                <div class="action-buttons">
                  <button class="btn-blue-light compact" @click="editContract(scope.row)">
                    <i class="fas fa-edit"></i>
                    <span>编辑</span>
                  </button>
                </div>
              </template>
            </el-table-column>
          </ErpTable>
        </el-tab-pane>
        <el-tab-pane label="履约监控" name="performance">
          <ErpTable :data="performanceList">
            <el-table-column prop="contractNo" label="合同编号" />
            <el-table-column prop="name" label="合同名称" />
            <el-table-column prop="progress" label="履约进度" />
            <el-table-column prop="milestone" label="当前节点" />
            <el-table-column prop="nextDate" label="下次节点" />
            <el-table-column prop="risk" label="风险等级" />
            <el-table-column prop="score" label="履约评分" />
            <el-table-column label="操作">
              <template #default="scope">
                <div class="action-buttons">
                  <button class="btn-blue-light compact" @click="viewPerformance(scope.row)">
                    <i class="fas fa-chart-line"></i>
                    <span>分析</span>
                  </button>
                </div>
              </template>
            </el-table-column>
          </ErpTable>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>

  <!-- 新建/编辑合同弹窗 -->
  <el-dialog v-model="dialogVisible" :title="dialogMode === 'add' ? '新建合同' : '编辑合同'" width="600px">
    <el-form :model="form" label-width="100px">
      <el-form-item label="合同编号">
        <el-input v-model="form.contractNo" placeholder="自动生成" :disabled="dialogMode === 'add'" />
      </el-form-item>
      <el-form-item label="合同名称">
        <el-input v-model="form.name" placeholder="请输入合同名称" />
      </el-form-item>
      <el-form-item label="甲方">
        <el-input v-model="form.partyA" placeholder="请输入甲方名称" />
      </el-form-item>
      <el-form-item label="乙方">
        <el-input v-model="form.partyB" placeholder="请输入乙方名称" />
      </el-form-item>
      <el-form-item label="合同金额">
        <el-input v-model="form.amount" type="number" placeholder="请输入合同金额" />
      </el-form-item>
      <el-form-item label="签署日期">
        <el-date-picker
          v-model="form.date"
          type="date"
          placeholder="选择签署日期"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="合同状态">
        <el-select v-model="form.status" placeholder="请选择合同状态" style="width: 100%">
          <el-option label="待签署" value="待签署" />
          <el-option label="履约中" value="履约中" />
          <el-option label="已完成" value="已完成" />
          <el-option label="已归档" value="已归档" />
        </el-select>
      </el-form-item>
      <el-form-item label="合同描述">
        <el-input v-model="form.description" type="textarea" placeholder="请输入合同描述和要求" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="flex justify-end space-x-4">
        <button class="btn-gray" @click="dialogVisible = false">
          <i class="fas fa-times"></i>
          <span>取消</span>
        </button>
        <button class="btn-blue" @click="saveContract">
          <i class="fas fa-save"></i>
          <span>保存</span>
        </button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ErpTable from '../components/common/ErpTable.vue'

// 定义合同表单数据类型
interface ContractForm {
  id: number
  contractNo: string
  name: string
  partyA: string
  partyB: string
  amount: number | string
  date: string
  status: string
  description: string
}

const activeTab = ref('contract')

// 弹窗相关状态
const dialogVisible = ref(false)
const dialogMode = ref<'add' | 'edit'>('add')
const form = ref<ContractForm>({
  id: 0,
  contractNo: '',
  name: '',
  partyA: '',
  partyB: '',
  amount: '',
  date: '',
  status: '',
  description: ''
})

// 合并的合同管理数据（录入+归档）
const contractList = ref([
  { contractNo: 'C-001', name: '原料采购合同', partyA: '本公司', partyB: '供应商A', amount: 12000, status: '履约中', date: '2024-06-01' },
  { contractNo: 'C-002', name: '设备采购合同', partyA: '本公司', partyB: '供应商B', amount: 35000, status: '待签署', date: '2024-06-02' },
  { contractNo: 'C-003', name: '运输服务合同', partyA: '本公司', partyB: '物流公司', amount: 8000, status: '已归档', date: '2024-05-15' },
  { contractNo: 'C-004', name: '技术服务合同', partyA: '本公司', partyB: '技术公司', amount: 25000, status: '履约中', date: '2024-06-05' },
  { contractNo: 'C-005', name: '租赁合同', partyA: '本公司', partyB: '租赁公司', amount: 15000, status: '已完成', date: '2024-05-20' },
  { contractNo: 'C-006', name: '销售合同', partyA: '客户A', partyB: '本公司', amount: 45000, status: '履约中', date: '2024-06-03' },
  { contractNo: 'C-007', name: '维护合同', partyA: '本公司', partyB: '维护公司', amount: 18000, status: '待审核', date: '2024-06-06' }
])

// 履约监控数据
const performanceList = ref([
  { contractNo: 'C-001', name: '原料采购合同', progress: '75%', milestone: '第三批交付', nextDate: '2024-06-15', risk: '低', score: '95分' },
  { contractNo: 'C-002', name: '设备采购合同', progress: '25%', milestone: '合同签署', nextDate: '2024-06-10', risk: '中', score: '88分' },
  { contractNo: 'C-004', name: '技术服务合同', progress: '60%', milestone: '阶段验收', nextDate: '2024-06-20', risk: '低', score: '92分' },
  { contractNo: 'C-006', name: '销售合同', progress: '40%', milestone: '产品交付', nextDate: '2024-06-18', risk: '高', score: '78分' },
  { contractNo: 'C-007', name: '维护合同', progress: '10%', milestone: '合同审核', nextDate: '2024-06-08', risk: '中', score: '85分' },
  { contractNo: 'C-008', name: '咨询合同', progress: '90%', milestone: '项目收尾', nextDate: '2024-06-12', risk: '低', score: '96分' },
  { contractNo: 'C-009', name: '培训合同', progress: '50%', milestone: '中期培训', nextDate: '2024-06-25', risk: '低', score: '90分' }
])

// 获取状态样式类
function getStatusClass(status: string) {
  const statusMap: { [key: string]: string } = {
    '履约中': 'status-tag status-active',
    '待签署': 'status-tag status-pending',
    '已归档': 'status-tag status-archived',
    '已完成': 'status-tag status-completed',
    '待审核': 'status-tag status-review'
  }
  return statusMap[status] || 'status-tag status-default'
}

// 新建合同
function createContract() {
  dialogMode.value = 'add'
  form.value = {
    id: 0,
    contractNo: generateContractNo(),
    name: '',
    partyA: '本公司',
    partyB: '',
    amount: '',
    date: new Date().toISOString().split('T')[0],
    status: '待签署',
    description: ''
  }
  dialogVisible.value = true
}

// 编辑合同
function editContract(row: any) {
  dialogMode.value = 'edit'
  form.value = {
    id: row.id || 0,
    contractNo: row.contractNo,
    name: row.name,
    partyA: row.partyA,
    partyB: row.partyB,
    amount: row.amount,
    date: row.date,
    status: row.status,
    description: row.description || ''
  }
  dialogVisible.value = true
}

// 生成合同编号
function generateContractNo(): string {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
  return `C-${year}${month}${day}-${random}`
}

// 保存合同
function saveContract() {
  if (!form.value.name || !form.value.partyA || !form.value.partyB || !form.value.amount) {
    console.warn('请填写完整信息')
    return
  }

  const amount = typeof form.value.amount === 'string'
    ? parseFloat(form.value.amount)
    : form.value.amount

  if (dialogMode.value === 'add') {
    // 新增合同
    const newContract = {
      contractNo: form.value.contractNo,
      name: form.value.name,
      partyA: form.value.partyA,
      partyB: form.value.partyB,
      amount: amount,
      status: form.value.status,
      date: form.value.date
    }
    contractList.value.unshift(newContract)
  } else {
    // 编辑合同
    const index = contractList.value.findIndex(c => c.contractNo === form.value.contractNo)
    if (index !== -1) {
      contractList.value[index] = {
        ...contractList.value[index],
        name: form.value.name,
        partyA: form.value.partyA,
        partyB: form.value.partyB,
        amount: amount,
        status: form.value.status,
        date: form.value.date
      }
    }
  }

  dialogVisible.value = false
}

function viewPerformance(row: any) { /* TODO: 查看履约分析逻辑 */ }
</script>

<style scoped>
.erp-dashboard-page {
  padding-bottom: 20px;
}

/* 状态标签样式 */
.status-tag {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  min-width: 60px;
}

/* 履约中 - 蓝色 */
.status-active {
  background-color: #e3f2fd;
  color: #1976d2;
  border: 1px solid #bbdefb;
}

/* 待签署 - 橙色 */
.status-pending {
  background-color: #fff3e0;
  color: #f57c00;
  border: 1px solid #ffcc02;
}

/* 已归档 - 灰色 */
.status-archived {
  background-color: #f5f5f5;
  color: #616161;
  border: 1px solid #e0e0e0;
}

/* 已完成 - 绿色 */
.status-completed {
  background-color: #e8f5e8;
  color: #388e3c;
  border: 1px solid #c8e6c9;
}

/* 待审核 - 紫色 */
.status-review {
  background-color: #f3e5f5;
  color: #7b1fa2;
  border: 1px solid #e1bee7;
}

/* 默认状态 */
.status-default {
  background-color: #f0f0f0;
  color: #666666;
  border: 1px solid #d0d0d0;
}
</style>