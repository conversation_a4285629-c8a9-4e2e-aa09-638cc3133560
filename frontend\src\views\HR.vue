<template>
  <ErpContent v-model="activeTab">
    <template #stats>
      <div class="erp-stat-card">
        <div class="erp-stat-label">员工总数</div>
        <div class="erp-stat-value">{{ employeeList.length }}</div>
        <i class="fas fa-users erp-stat-icon blue"></i>
      </div>
      <div class="erp-stat-card">
        <div class="erp-stat-label">在职员工</div>
        <div class="erp-stat-value">{{ activeEmployeeCount }}</div>
        <i class="fas fa-user-check erp-stat-icon green"></i>
      </div>
      <div class="erp-stat-card">
        <div class="erp-stat-label">本月薪资总额</div>
        <div class="erp-stat-value">¥{{ totalSalaryAmount }}</div>
        <i class="fas fa-money-bill-wave erp-stat-icon purple"></i>
      </div>
      <div class="erp-stat-card">
        <div class="erp-stat-label">本月出勤率</div>
        <div class="erp-stat-value">{{ attendanceRate }}%</div>
        <i class="fas fa-calendar-check erp-stat-icon red"></i>
      </div>
    </template>

    <template #toolbar>
      <!-- 员工管理工具栏 -->
      <div v-if="activeTab === 'employee'" class="erp-table-toolbar">
        <div class="first-row">
          <el-input v-model="search.keyword" placeholder="员工姓名/工号/部门" size="small" clearable />
          <el-select v-model="search.department" placeholder="部门筛选" size="small" clearable>
            <el-option label="生产部" value="生产部" />
            <el-option label="仓库" value="仓库" />
            <el-option label="财务部" value="财务部" />
            <el-option label="人事部" value="人事部" />
          </el-select>
          <el-select v-model="search.status" placeholder="状态筛选" size="small" clearable>
            <el-option label="在职" value="在职" />
            <el-option label="离职" value="离职" />
            <el-option label="试用期" value="试用期" />
          </el-select>
          <button class="btn-blue" @click="searchItems">
            <i class="fas fa-search"></i>
            <span>搜索</span>
          </button>
          <button class="btn-gray" @click="resetSearch">
            <i class="fas fa-undo"></i>
            <span>重置</span>
          </button>
        </div>
        <div class="second-row">
          <button class="btn-blue" @click="addEmployee">
            <i class="fas fa-plus"></i>
            <span>新增员工</span>
          </button>
          <button class="btn-green-light" @click="exportEmployees">
            <i class="fas fa-file-export"></i>
            <span>导出员工</span>
          </button>
          <button class="btn-gray" @click="importEmployees">
            <i class="fas fa-file-import"></i>
            <span>导入员工</span>
          </button>
        </div>
      </div>

      <!-- 薪资核算工具栏 -->
      <div v-if="activeTab === 'salary'" class="erp-table-toolbar">
        <div class="first-row">
          <el-input v-model="search.keyword" placeholder="员工姓名/工号" size="small" clearable />
          <el-date-picker v-model="search.month" type="month" placeholder="选择月份" size="small" clearable />
          <el-input v-model="search.minSalary" placeholder="最低工资" size="small" clearable style="width: 140px;" />
          <el-input v-model="search.maxSalary" placeholder="最高工资" size="small" clearable style="width: 140px;" />
          <button class="btn-blue" @click="searchItems">
            <i class="fas fa-search"></i>
            <span>搜索</span>
          </button>
          <button class="btn-gray" @click="resetSearch">
            <i class="fas fa-undo"></i>
            <span>重置</span>
          </button>
        </div>
        <div class="second-row">
          <button class="btn-blue" @click="batchCalculate">
            <i class="fas fa-calculator"></i>
            <span>批量工资计算</span>
          </button>
          <button class="btn-green-light" @click="exportSalaries">
            <i class="fas fa-file-export"></i>
            <span>导出薪资</span>
          </button>
          <button class="btn-gray" @click="printSalaries">
            <i class="fas fa-print"></i>
            <span>打印工资条</span>
          </button>
        </div>
      </div>

      <!-- 考勤管理工具栏 -->
      <div v-if="activeTab === 'attendance'" class="erp-table-toolbar">
        <div class="first-row">
          <el-input v-model="search.keyword" placeholder="员工姓名" size="small" clearable />
          <el-date-picker v-model="search.dates" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" size="small" clearable />
          <el-select v-model="search.attendanceStatus" placeholder="考勤状态" size="small" clearable>
            <el-option label="正常" value="正常" />
            <el-option label="迟到" value="迟到" />
            <el-option label="早退" value="早退" />
            <el-option label="缺勤" value="缺勤" />
          </el-select>
          <button class="btn-blue" @click="searchItems">
            <i class="fas fa-search"></i>
            <span>搜索</span>
          </button>
          <button class="btn-gray" @click="resetSearch">
            <i class="fas fa-undo"></i>
            <span>重置</span>
          </button>
        </div>
        <div class="second-row">
          <button class="btn-blue" @click="addAttendanceRecord">
            <i class="fas fa-plus"></i>
            <span>添加考勤</span>
          </button>
          <button class="btn-green-light" @click="exportAttendance">
            <i class="fas fa-file-export"></i>
            <span>导出考勤</span>
          </button>
        </div>
      </div>

      <!-- 请假/加班工具栏 -->
      <div v-if="activeTab === 'leave'" class="erp-table-toolbar">
        <div class="first-row">
          <el-input v-model="search.keyword" placeholder="员工姓名" size="small" clearable />
          <el-select v-model="search.leaveType" placeholder="类型筛选" size="small" clearable>
            <el-option label="请假" value="请假" />
            <el-option label="加班" value="加班" />
          </el-select>
          <el-select v-model="search.leaveStatus" placeholder="状态筛选" size="small" clearable>
            <el-option label="已批" value="已批" />
            <el-option label="待批" value="待批" />
            <el-option label="拒绝" value="拒绝" />
          </el-select>
          <el-date-picker v-model="search.dates" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" size="small" clearable />
          <button class="btn-blue" @click="searchItems">
            <i class="fas fa-search"></i>
            <span>搜索</span>
          </button>
          <button class="btn-gray" @click="resetSearch">
            <i class="fas fa-undo"></i>
            <span>重置</span>
          </button>
        </div>
        <div class="second-row">
          <button class="btn-blue" @click="addLeaveRequest">
            <i class="fas fa-plus"></i>
            <span>新增申请</span>
          </button>
          <button class="btn-green-light" @click="batchApproveLeave" :disabled="selectedLeaves.length === 0">
            <i class="fas fa-check"></i>
            <span>批量审批 ({{ selectedLeaves.length }})</span>
          </button>
          <button class="btn-red-light" @click="batchRejectLeave" :disabled="selectedLeaves.length === 0">
            <i class="fas fa-times"></i>
            <span>批量拒绝 ({{ selectedLeaves.length }})</span>
          </button>
        </div>
      </div>

      <!-- 人事报表工具栏 -->
      <div v-if="activeTab === 'report'" class="erp-table-toolbar">
        <div class="first-row">
          <el-input v-model="search.keyword" placeholder="报表类型" size="small" clearable />
          <el-date-picker v-model="search.dates" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" size="small" clearable />
          <button class="btn-blue" @click="searchItems">
            <i class="fas fa-search"></i>
            <span>搜索</span>
          </button>
          <button class="btn-gray" @click="resetSearch">
            <i class="fas fa-undo"></i>
            <span>重置</span>
          </button>
        </div>
        <div class="second-row">
          <button class="btn-blue" @click="generateReport">
            <i class="fas fa-chart-bar"></i>
            <span>生成报表</span>
          </button>
          <button class="btn-green-light" @click="exportReports">
            <i class="fas fa-file-export"></i>
            <span>导出报表</span>
          </button>
          <button class="btn-gray" @click="refreshReports">
            <i class="fas fa-sync"></i>
            <span>刷新数据</span>
          </button>
        </div>
      </div>
    </template>

    <template #tabs="{ activeTab }">
      <el-tab-pane label="员工管理" name="employee">
        <ErpTable
          ref="employeeTableRef"
          :data="filteredEmployeeList"
          max-height="600"
          style="width: 100%"
          table-layout="auto"
        >
          <el-table-column prop="id" label="工号" />
          <el-table-column prop="name" label="姓名" />
          <el-table-column prop="department" label="部门" />
          <el-table-column prop="position" label="职位" />
          <el-table-column prop="status" label="状态">
            <template #default="scope">
              <el-tag :type="scope.row.status === '在职' ? 'success' : 'info'">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="160">
            <template #default="scope">
              <div class="action-buttons">
                <button class="btn-blue-light compact" @click="editEmployee(scope.row)">
                  <i class="fas fa-edit"></i>
                  <span>编辑</span>
                </button>
                <button class="btn-red-light compact" @click="deleteEmployee(scope.row)">
                  <i class="fas fa-trash"></i>
                  <span>删除</span>
                </button>
              </div>
            </template>
          </el-table-column>
        </ErpTable>
      </el-tab-pane>
      <el-tab-pane label="薪资核算" name="salary">
        <ErpTable
          :data="filteredSalaryList"
          max-height="600"
          style="width: 100%"
          table-layout="auto"
        >
          <el-table-column prop="name" label="姓名" />
          <el-table-column prop="month" label="月份" />
          <el-table-column prop="base" label="基本工资" align="right">
            <template #default="scope">
              ¥{{ scope.row.base.toLocaleString('zh-CN') }}
            </template>
          </el-table-column>
          <el-table-column prop="bonus" label="奖金" align="right">
            <template #default="scope">
              ¥{{ scope.row.bonus.toLocaleString('zh-CN') }}
            </template>
          </el-table-column>
          <el-table-column prop="total" label="实发工资" align="right">
            <template #default="scope">
              <span class="font-semibold text-blue-600">
                ¥{{ scope.row.total.toLocaleString('zh-CN') }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="160">
            <template #default="scope">
              <div class="action-buttons">
                <button class="btn-blue-light compact" @click="viewSalaryDetail(scope.row)">
                  <i class="fas fa-eye"></i>
                  <span>详情</span>
                </button>
                <button class="btn-green-light compact" @click="exportSalary(scope.row)">
                  <i class="fas fa-file-export"></i>
                  <span>导出</span>
                </button>
              </div>
            </template>
          </el-table-column>
        </ErpTable>
      </el-tab-pane>
      <el-tab-pane label="考勤管理" name="attendance">
        <ErpTable
          :data="filteredAttendanceList"
          max-height="600"
          style="width: 100%"
          table-layout="auto"
        >
          <el-table-column prop="name" label="姓名" />
          <el-table-column prop="date" label="日期" />
          <el-table-column prop="status" label="状态">
            <template #default="scope">
              <el-tag :type="getAttendanceStatusType(scope.row.status)">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="checkin" label="上班打卡" align="center" />
          <el-table-column prop="checkout" label="下班打卡" align="center" />
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <div class="action-buttons">
                <button class="btn-blue-light compact" @click="viewAttendanceDetail(scope.row)">
                  <i class="fas fa-eye"></i>
                  <span>详情</span>
                </button>
              </div>
            </template>
          </el-table-column>
        </ErpTable>
      </el-tab-pane>
      <el-tab-pane label="请假/加班" name="leave">
        <ErpTable
          ref="leaveTableRef"
          :data="filteredLeaveList"
          @selection-change="handleLeaveSelectionChange"
          max-height="600"
          style="width: 100%"
          table-layout="auto"
        >
          <el-table-column type="selection" width="40" />
          <el-table-column prop="name" label="姓名" />
          <el-table-column prop="type" label="类型">
            <template #default="scope">
              <el-tag :type="scope.row.type === '请假' ? 'warning' : 'info'">
                {{ scope.row.type }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="date" label="日期" />
          <el-table-column prop="duration" label="时长" align="center" />
          <el-table-column prop="status" label="状态">
            <template #default="scope">
              <el-tag :type="getLeaveStatusType(scope.row.status)">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="160">
            <template #default="scope">
              <div class="action-buttons">
                <button
                  class="btn-green-light compact"
                  @click="approveLeave(scope.row)"
                  :disabled="scope.row.status === '已批'"
                >
                  <i class="fas fa-check"></i>
                  <span>审批</span>
                </button>
                <button
                  class="btn-red-light compact"
                  @click="rejectLeave(scope.row)"
                  :disabled="scope.row.status === '拒绝'"
                >
                  <i class="fas fa-times"></i>
                  <span>拒绝</span>
                </button>
              </div>
            </template>
          </el-table-column>
        </ErpTable>
      </el-tab-pane>
      <el-tab-pane label="人事报表" name="report">
        <ErpTable
          :data="filteredReportList"
          max-height="600"
          style="width: 100%"
          table-layout="auto"
        >
          <el-table-column prop="type" label="报表类型" />
          <el-table-column prop="value" label="数值" align="right">
            <template #default="scope">
              <span class="font-semibold text-green-600">
                {{ scope.row.value }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="date" label="日期" align="center" />
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <div class="action-buttons">
                <button class="btn-green-light compact" @click="exportReport(scope.row)">
                  <i class="fas fa-file-export"></i>
                  <span>导出</span>
                </button>
              </div>
            </template>
          </el-table-column>
        </ErpTable>
      </el-tab-pane>
    </template>
  </ErpContent>

  <!-- 新增/编辑员工弹窗 -->
  <el-dialog v-model="dialogVisible" :title="dialogMode === 'add' ? '新增员工' : '编辑员工'" width="600px">
    <el-form :model="form" label-width="100px">
      <el-form-item label="员工工号">
        <el-input v-model="form.id" placeholder="请输入员工工号或自动生成" />
      </el-form-item>
      <el-form-item label="员工姓名">
        <el-input v-model="form.name" placeholder="请输入员工姓名" />
      </el-form-item>
      <el-form-item label="所属部门">
        <el-select v-model="form.department" placeholder="请选择部门" style="width: 100%">
          <el-option label="生产部" value="生产部" />
          <el-option label="仓库" value="仓库" />
          <el-option label="财务部" value="财务部" />
          <el-option label="人事部" value="人事部" />
          <el-option label="技术部" value="技术部" />
          <el-option label="销售部" value="销售部" />
          <el-option label="采购部" value="采购部" />
        </el-select>
      </el-form-item>
      <el-form-item label="职位">
        <el-input v-model="form.position" placeholder="请输入职位" />
      </el-form-item>
      <el-form-item label="员工状态">
        <el-select v-model="form.status" placeholder="请选择员工状态" style="width: 100%">
          <el-option label="在职" value="在职" />
          <el-option label="试用期" value="试用期" />
          <el-option label="离职" value="离职" />
        </el-select>
      </el-form-item>
      <el-form-item label="入职日期">
        <el-date-picker
          v-model="form.hireDate"
          type="date"
          placeholder="选择入职日期"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="联系电话">
        <el-input v-model="form.phone" placeholder="请输入联系电话" />
      </el-form-item>
      <el-form-item label="备注">
        <el-input v-model="form.remark" type="textarea" placeholder="请输入备注信息" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="flex justify-end space-x-4">
        <button class="btn-gray" @click="dialogVisible = false">
          <i class="fas fa-times"></i>
          <span>取消</span>
        </button>
        <button class="btn-blue" @click="saveEmployee">
          <i class="fas fa-save"></i>
          <span>保存</span>
        </button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, watch } from 'vue'
import ErpContent from '../components/ErpContent.vue'
import ErpTable from '../components/common/ErpTable.vue'

// 定义员工表单数据类型
interface EmployeeForm {
  id: string
  name: string
  department: string
  position: string
  status: string
  hireDate: string
  phone: string
  remark: string
}

const activeTab = ref('employee')

// 表格引用
const employeeTableRef = ref()
const salaryTableRef = ref()
const attendanceTableRef = ref()
const leaveTableRef = ref()
const reportTableRef = ref()

// 选择状态管理
const selectedLeaves = ref([])

// 弹窗相关状态
const dialogVisible = ref(false)
const dialogMode = ref<'add' | 'edit'>('add')
const form = ref<EmployeeForm>({
  id: '',
  name: '',
  department: '',
  position: '',
  status: '在职',
  hireDate: '',
  phone: '',
  remark: ''
})

// 搜索条件
const search = ref({
  keyword: '',
  department: '',
  status: '',
  month: '',
  minSalary: '',
  maxSalary: '',
  dates: [],
  attendanceStatus: '',
  leaveType: '',
  leaveStatus: ''
})
const employeeList = ref([
  { id: 'E001', name: '张三', department: '生产部', position: '操作员', status: '在职' },
  { id: 'E002', name: '李四', department: '仓库', position: '管理员', status: '在职' }
])
const salaryList = ref([
  { name: '张三', month: '2024-06', base: 8000, bonus: 1200, total: 9200 },
  { name: '李四', month: '2024-06', base: 7500, bonus: 800, total: 8300 }
])
const attendanceList = ref([
  { name: '张三', date: '2024-06-01', status: '正常', checkin: '08:59', checkout: '18:01' },
  { name: '李四', date: '2024-06-01', status: '迟到', checkin: '09:12', checkout: '18:03' }
])
const leaveList = ref([
  { name: '张三', type: '请假', date: '2024-06-02', duration: '1天', status: '已批' },
  { name: '李四', type: '加班', date: '2024-06-03', duration: '2小时', status: '待批' },
  { name: '王五', type: '请假', date: '2024-06-04', duration: '0.5天', status: '待批' },
  { name: '赵六', type: '加班', date: '2024-06-05', duration: '3小时', status: '待批' },
  { name: '钱七', type: '请假', date: '2024-06-06', duration: '2天', status: '拒绝' }
])
const hrReportList = ref([
  { type: '本月出勤率', value: '98.2%', date: '2024-06' },
  { type: '本月薪资总额', value: '¥ 168,000', date: '2024-06' }
])

// 过滤后的数据
const filteredEmployeeList = computed(() => {
  let filtered = employeeList.value

  if (search.value.keyword) {
    filtered = filtered.filter(item =>
      item.name.includes(search.value.keyword) ||
      item.id.includes(search.value.keyword) ||
      item.department.includes(search.value.keyword)
    )
  }

  if (search.value.department) {
    filtered = filtered.filter(item => item.department === search.value.department)
  }

  if (search.value.status) {
    filtered = filtered.filter(item => item.status === search.value.status)
  }

  return filtered
})

const filteredSalaryList = computed(() => {
  let filtered = salaryList.value

  if (search.value.keyword) {
    filtered = filtered.filter(item =>
      item.name.includes(search.value.keyword)
    )
  }

  if (search.value.month) {
    const searchMonth = search.value.month.substring(0, 7) // YYYY-MM格式
    filtered = filtered.filter(item => item.month === searchMonth)
  }

  if (search.value.minSalary && !isNaN(Number(search.value.minSalary))) {
    filtered = filtered.filter(item => item.total >= Number(search.value.minSalary))
  }

  if (search.value.maxSalary && !isNaN(Number(search.value.maxSalary))) {
    filtered = filtered.filter(item => item.total <= Number(search.value.maxSalary))
  }

  return filtered
})

const filteredAttendanceList = computed(() => {
  let filtered = attendanceList.value

  if (search.value.keyword) {
    filtered = filtered.filter(item =>
      item.name.includes(search.value.keyword)
    )
  }

  if (search.value.attendanceStatus) {
    filtered = filtered.filter(item => item.status === search.value.attendanceStatus)
  }

  if (search.value.dates && search.value.dates.length === 2) {
    const [startDate, endDate] = search.value.dates
    filtered = filtered.filter(item => {
      const itemDate = new Date(item.date)
      return itemDate >= startDate && itemDate <= endDate
    })
  }

  return filtered
})

const filteredLeaveList = computed(() => {
  let filtered = leaveList.value

  if (search.value.keyword) {
    filtered = filtered.filter(item =>
      item.name.includes(search.value.keyword)
    )
  }

  if (search.value.leaveType) {
    filtered = filtered.filter(item => item.type === search.value.leaveType)
  }

  if (search.value.leaveStatus) {
    filtered = filtered.filter(item => item.status === search.value.leaveStatus)
  }

  if (search.value.dates && search.value.dates.length === 2) {
    const [startDate, endDate] = search.value.dates
    filtered = filtered.filter(item => {
      const itemDate = new Date(item.date)
      return itemDate >= startDate && itemDate <= endDate
    })
  }

  return filtered
})

const filteredReportList = computed(() => {
  let filtered = hrReportList.value

  if (search.value.keyword) {
    filtered = filtered.filter(item =>
      item.type.includes(search.value.keyword)
    )
  }

  if (search.value.dates && search.value.dates.length === 2) {
    const [startDate, endDate] = search.value.dates
    filtered = filtered.filter(item => {
      const itemDate = new Date(item.date + '-01') // 假设date格式为YYYY-MM
      return itemDate >= startDate && itemDate <= endDate
    })
  }

  return filtered
})

// 统计数据计算属性
const activeEmployeeCount = computed(() => {
  return employeeList.value.filter(emp => emp.status === '在职').length
})

const totalSalaryAmount = computed(() => {
  const total = salaryList.value.reduce((sum, item) => sum + item.total, 0)
  return total.toLocaleString('zh-CN')
})

const attendanceRate = computed(() => {
  const totalRecords = attendanceList.value.length
  if (totalRecords === 0) return '0'

  const normalRecords = attendanceList.value.filter(record => record.status === '正常').length
  return ((normalRecords / totalRecords) * 100).toFixed(1)
})

// 监听标签页切换，刷新对应的表格
watch(activeTab, (newTab) => {
  nextTick(() => {
    setTimeout(() => {
      switch (newTab) {
        case 'employee':
          employeeTableRef.value?.refreshTable()
          break
        case 'salary':
          salaryTableRef.value?.refreshTable()
          break
        case 'attendance':
          attendanceTableRef.value?.refreshTable()
          break
        case 'leave':
          leaveTableRef.value?.refreshTable()
          break
        case 'report':
          reportTableRef.value?.refreshTable()
          break
      }
    }, 100)
  })
})
// 状态标签类型辅助函数
function getAttendanceStatusType(status: string) {
  switch (status) {
    case '正常': return 'success'
    case '迟到': return 'warning'
    case '早退': return 'danger'
    case '缺勤': return 'danger'
    default: return 'info'
  }
}

function getLeaveStatusType(status: string) {
  switch (status) {
    case '已批': return 'success'
    case '待批': return 'warning'
    case '拒绝': return 'danger'
    default: return 'info'
  }
}

// 搜索和重置功能
function searchItems() {
  console.log('搜索条件:', search.value)
  // 搜索逻辑已通过计算属性实现
}

function resetSearch() {
  search.value = {
    keyword: '',
    department: '',
    status: '',
    month: '',
    minSalary: '',
    maxSalary: '',
    dates: [],
    attendanceStatus: '',
    leaveType: '',
    leaveStatus: ''
  }
}

// 业务逻辑函数
// 新增员工
function addEmployee() {
  dialogMode.value = 'add'
  form.value = {
    id: generateEmployeeId(),
    name: '',
    department: '',
    position: '',
    status: '在职',
    hireDate: new Date().toISOString().split('T')[0],
    phone: '',
    remark: ''
  }
  dialogVisible.value = true
}

// 编辑员工
function editEmployee(row: any) {
  dialogMode.value = 'edit'
  form.value = {
    id: row.id,
    name: row.name,
    department: row.department,
    position: row.position,
    status: row.status,
    hireDate: row.hireDate || new Date().toISOString().split('T')[0],
    phone: row.phone || '',
    remark: row.remark || ''
  }
  dialogVisible.value = true
}

// 生成员工工号
function generateEmployeeId(): string {
  const now = new Date()
  const year = now.getFullYear().toString().slice(-2)
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
  return `E${year}${month}${random}`
}

// 保存员工
function saveEmployee() {
  if (!form.value.name || !form.value.department || !form.value.position) {
    console.warn('请填写完整信息')
    return
  }

  if (dialogMode.value === 'add') {
    // 新增员工
    const newEmployee = {
      id: form.value.id,
      name: form.value.name,
      department: form.value.department,
      position: form.value.position,
      status: form.value.status
    }
    employeeList.value.unshift(newEmployee)
  } else {
    // 编辑员工
    const index = employeeList.value.findIndex(e => e.id === form.value.id)
    if (index !== -1) {
      employeeList.value[index] = {
        ...employeeList.value[index],
        name: form.value.name,
        department: form.value.department,
        position: form.value.position,
        status: form.value.status
      }
    }
  }

  dialogVisible.value = false
}

function deleteEmployee(row: any) { /* TODO: 删除员工逻辑 */ }
function exportEmployees() { /* TODO: 导出员工逻辑 */ }
function importEmployees() { /* TODO: 导入员工逻辑 */ }

function batchCalculate() { /* TODO: 批量工资计算逻辑 */ }
function viewSalaryDetail(row: any) { /* TODO: 查看薪资详情逻辑 */ }
function exportSalary(row: any) { /* TODO: 导出薪资逻辑 */ }
function exportSalaries() { /* TODO: 导出薪资逻辑 */ }
function printSalaries() { /* TODO: 打印工资条逻辑 */ }

function addAttendanceRecord() { /* TODO: 添加考勤记录逻辑 */ }
function viewAttendanceDetail(row: any) { /* TODO: 查看考勤详情逻辑 */ }
function exportAttendance() { /* TODO: 导出考勤逻辑 */ }

// 选择处理函数
function handleLeaveSelectionChange(selection: any[]) {
  selectedLeaves.value = selection
}

function addLeaveRequest() { /* TODO: 新增请假/加班申请逻辑 */ }

function approveLeave(row: any) {
  // 更新单个记录状态
  const index = leaveList.value.findIndex(item => item === row)
  if (index !== -1) {
    leaveList.value[index].status = '已批'
  }
}

function rejectLeave(row: any) {
  // 更新单个记录状态
  const index = leaveList.value.findIndex(item => item === row)
  if (index !== -1) {
    leaveList.value[index].status = '拒绝'
  }
}

function batchApproveLeave() {
  // 批量更新选中记录的状态为"已批"
  selectedLeaves.value.forEach(selectedItem => {
    const index = leaveList.value.findIndex(item => item === selectedItem)
    if (index !== -1 && leaveList.value[index].status === '待批') {
      leaveList.value[index].status = '已批'
    }
  })
  // 清空选择
  selectedLeaves.value = []
}

function batchRejectLeave() {
  // 批量更新选中记录的状态为"拒绝"
  selectedLeaves.value.forEach(selectedItem => {
    const index = leaveList.value.findIndex(item => item === selectedItem)
    if (index !== -1 && leaveList.value[index].status === '待批') {
      leaveList.value[index].status = '拒绝'
    }
  })
  // 清空选择
  selectedLeaves.value = []
}

function generateReport() { /* TODO: 生成报表逻辑 */ }
function exportReport(row: any) { /* TODO: 导出报表逻辑 */ }
function exportReports() { /* TODO: 导出报表逻辑 */ }
function refreshReports() { /* TODO: 刷新报表数据逻辑 */ }
</script>

<style scoped>
/* HR页面特定样式 */
.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: center;
}

.action-buttons .compact {
  padding: 6px 12px;
  font-size: 12px;
}

/* 中等屏幕优化 */
@media (max-width: 1200px) {
  .erp-table-toolbar .first-row .el-input,
  .erp-table-toolbar .first-row .el-select {
    max-width: 160px !important;
  }

  .erp-table-toolbar .first-row .el-input[placeholder*="最低工资"],
  .erp-table-toolbar .first-row .el-input[placeholder*="最高工资"] {
    max-width: 100px !important;
  }
}

/* HR页面工具栏响应式样式 - 仅移动端滚动 */
@media (max-width: 768px) {
  .erp-table-toolbar .first-row {
    overflow-x: auto !important;
    overflow-y: hidden !important;
    flex-wrap: nowrap !important;
    -webkit-overflow-scrolling: touch !important;
    scrollbar-width: none !important; /* Firefox隐藏滚动条 */
    -ms-overflow-style: none !important; /* IE隐藏滚动条 */
  }

  .erp-table-toolbar .first-row::-webkit-scrollbar {
    display: none !important; /* Webkit隐藏滚动条 */
  }

  /* 移动端进一步缩小 */
  .erp-table-toolbar .first-row .el-input,
  .erp-table-toolbar .first-row .el-select {
    min-width: 100px !important;
    max-width: 140px !important;
  }

  .erp-table-toolbar .first-row .el-input[placeholder*="最低工资"],
  .erp-table-toolbar .first-row .el-input[placeholder*="最高工资"] {
    min-width: 80px !important;
    max-width: 90px !important;
  }
}

/* 小屏幕进一步优化 */
@media (max-width: 480px) {
  .erp-table-toolbar .first-row .el-input,
  .erp-table-toolbar .first-row .el-select {
    min-width: 90px !important;
    max-width: 120px !important;
  }

  .erp-table-toolbar .first-row .el-input[placeholder*="员工姓名/工号/部门"] {
    min-width: 110px !important;
  }

  .erp-table-toolbar .first-row .el-input[placeholder*="最低工资"],
  .erp-table-toolbar .first-row .el-input[placeholder*="最高工资"] {
    min-width: 70px !important;
    max-width: 80px !important;
  }
}

/* 确保工具栏内容不超出容器 */
.erp-table-toolbar {
  width: 100%;
  box-sizing: border-box;
}

.erp-table-toolbar .first-row,
.erp-table-toolbar .second-row {
  width: 100%;
  box-sizing: border-box;
}

/* 输入框自适应宽度 */
.erp-table-toolbar .first-row .el-input,
.erp-table-toolbar .first-row .el-select {
  flex: 1 1 auto !important;
  min-width: 120px !important; /* 基础最小宽度 */
  max-width: 200px !important; /* 最大宽度限制 */
}

/* 根据placeholder内容设置不同的最小宽度 */
.erp-table-toolbar .first-row .el-input[placeholder*="员工姓名/工号/部门"] {
  min-width: 140px !important;
}

.erp-table-toolbar .first-row .el-input[placeholder*="员工姓名/工号"] {
  min-width: 120px !important;
}

.erp-table-toolbar .first-row .el-input[placeholder*="最低工资"],
.erp-table-toolbar .first-row .el-input[placeholder*="最高工资"] {
  min-width: 100px !important;
  max-width: 120px !important;
}

.erp-table-toolbar .first-row .el-input[placeholder*="报表类型"] {
  min-width: 100px !important;
}

/* 下拉选择器自适应 */
.erp-table-toolbar .first-row .el-select[placeholder*="部门筛选"] {
  min-width: 100px !important;
  max-width: 120px !important;
}

.erp-table-toolbar .first-row .el-select[placeholder*="状态筛选"],
.erp-table-toolbar .first-row .el-select[placeholder*="类型筛选"],
.erp-table-toolbar .first-row .el-select[placeholder*="考勤状态"] {
  min-width: 100px !important;
  max-width: 120px !important;
}

/* 月份选择器 */
.erp-table-toolbar .first-row .el-date-picker[placeholder*="选择月份"] {
  flex: 0 0 auto !important;
  min-width: 120px !important;
  max-width: 140px !important;
}

/* 日期范围选择器样式 */
.first-row .el-date-editor--daterange,
.first-row .el-date-editor.el-input__wrapper.el-range-editor {
  width: 260px !important;
  max-width: 260px !important;
  box-sizing: border-box !important;
}

/* 金额显示样式 */
.font-semibold {
  font-weight: 600;
}

.text-blue-600 {
  color: #2563eb;
}

.text-green-600 {
  color: #16a34a;
}
</style>